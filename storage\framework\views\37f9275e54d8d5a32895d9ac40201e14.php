<textarea class="form-control <?php $__errorArgs = [$model];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model.debounce.500ms="<?php echo e($model); ?>" id="<?php echo e($model); ?>" rows="4" <?php echo e($attributes); ?>></textarea>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/form/textarea.blade.php ENDPATH**/ ?>