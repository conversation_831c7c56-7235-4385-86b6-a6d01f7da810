@props([
    'currentStep' => 1,
    'totalSteps' => 1,
    'isFirstStep' => true,
    'isLastStep' => false,
    'showPrevious' => true,
    'showNext' => true,
    'showSubmit' => true,
    'previousText' => 'Previous',
    'nextText' => 'Next',
    'submitText' => 'Submit',
    'previousAction' => 'goToPrevStep',
    'nextAction' => 'goToNextStep',
    'submitAction' => 'submitForm'
])

<div class="wizard-actions">
    <div class="d-flex justify-content-between">
        <div>
            @if(!$isFirstStep && $showPrevious)
                <button type="button" 
                        class="btn btn-light-primary font-weight-bold btn-md px-9 py-4" 
                        data-wizard-type="action-prev"
                        wire:click="{{ $previousAction }}">
                    <i class="ki ki-arrow-left icon-sm"></i>
                    {{ $previousText }}
                </button>
            @endif
        </div>
        
        <div>
            @if(!$isLastStep && $showNext)
                <button type="button" 
                        class="btn btn-primary font-weight-bold btn-md px-9 py-4" 
                        data-wizard-type="action-next"
                        wire:click="{{ $nextAction }}">
                    {{ $nextText }}
                    <i class="ki ki-arrow-right icon-sm"></i>
                </button>
            @elseif($isLastStep && $showSubmit)
                <button type="button" 
                        class="btn btn-success font-weight-bold btn-md px-9 py-4" 
                        data-wizard-type="action-submit"
                        wire:click="{{ $submitAction }}"
                        wire:loading.attr="disabled"
                        wire:loading.class="disabled">
                    <span wire:loading.remove wire:target="{{ $submitAction }}">
                        <i class="ki ki-check icon-sm"></i>
                        {{ $submitText }}
                    </span>
                    <span wire:loading wire:target="{{ $submitAction }}">
                        <i class="fas fa-spinner fa-spin mr-1"></i>
                        Submitting...
                    </span>
                </button>
            @endif
        </div>
    </div>
</div>
