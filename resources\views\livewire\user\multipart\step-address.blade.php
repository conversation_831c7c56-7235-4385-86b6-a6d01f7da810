<div class="row">
    <div class="col-md-12">
        <x-form.input 
            wire:model.defer="user.address" 
            name="user.address" 
            label="Address" 
            placeholder="Enter street address" />
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <x-form.input 
            wire:model.defer="user.city" 
            name="user.city" 
            label="City" 
            placeholder="Enter city" />
    </div>
    <div class="col-md-3">
        <x-form.select 
            wire:model.defer="user.state_id" 
            name="user.state_id" 
            label="State">
            <option value="">Select State</option>
            @foreach($states as $state)
                <option value="{{ $state->id }}">{{ $state->name }}</option>
            @endforeach
        </x-form.select>
    </div>
    <div class="col-md-3">
        <x-form.input 
            wire:model.defer="user.zip" 
            name="user.zip" 
            label="ZIP Code" 
            placeholder="12345 or 12345-6789" />
        <small class="form-text text-muted">Format: 12345 or 12345-6789</small>
    </div>
</div>

<div class="alert alert-info mt-4">
    <div class="alert-icon">
        <i class="ki ki-information-circle"></i>
    </div>
    <div class="alert-text">
        <strong>Step 4 of 5:</strong> Enter your address information. All fields are optional, but if you provide a ZIP code, it must be in the correct format (5 digits or 5+4 format).
    </div>
</div>
