@php
    use App\Models\User;
@endphp

<div>
    <x-multipart-form.wrapper 
        :title="$multipartConfig['title']"
        :showProgressBar="$multipartConfig['showProgressBar']"
        :allowStepNavigation="$multipartConfig['allowStepNavigation']"
        :currentStep="$currentStep"
        :totalSteps="$totalSteps"
        :steps="$multipartConfig['steps']">
        
        <form wire:submit.prevent="submitMultipartForm">
            
            <!-- Step 1: Basic Information -->
            <x-multipart-form.step-content 
                :stepNumber="1" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(1)"
                :description="$this->getStepDescription(1)">
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <x-form.input.text label="Email" labelRequired="1" model="user.email" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="First Name" labelRequired="1" model="user.first_name" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="Last Name" labelRequired="1" model="user.last_name" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <x-form.input.text label="Printed Name" labelRequired="1" model="user.printed_name" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <x-form.input.text label="Clinic Name" labelRequired="0" model="user.clinic_name" />
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="alert-icon">
                        <i class="ki ki-information-circle"></i>
                    </div>
                    <div class="alert-text">
                        <strong>Step 1 of 5:</strong> Please provide basic personal information. All required fields must be completed before proceeding to the next step.
                    </div>
                </div>
                
            </x-multipart-form.step-content>

            <!-- Step 2: Professional Credentials -->
            <x-multipart-form.step-content 
                :stepNumber="2" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(2)"
                :description="$this->getStepDescription(2)">
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <x-form.input.text label="NPI#" labelRequired="1" model="user.NPI#" type="number" placeholder="Enter 10-digit NPI number" />
                        <small class="form-text text-muted">Must be exactly 10 digits and unique</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="LIC#" labelRequired="0" model="user.LIC#" placeholder="Enter license number" />
                        <small class="form-text text-muted">At least 7 characters (excluding dashes)</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="DEA#" labelRequired="0" model="user.DEA#" placeholder="Enter DEA number" />
                        <small class="form-text text-muted">Format: 2 letters followed by 7 digits (e.g., *********)</small>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="alert-icon">
                        <i class="ki ki-information-circle"></i>
                    </div>
                    <div class="alert-text">
                        <strong>Step 2 of 5:</strong> Enter your professional credentials. The NPI number is required and must be unique. License and DEA numbers are optional but must follow the specified formats if provided.
                    </div>
                </div>
                
            </x-multipart-form.step-content>

            <!-- Step 3: Contact & Dispatch -->
            <x-multipart-form.step-content 
                :stepNumber="3" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(3)"
                :description="$this->getStepDescription(3)">
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="Phone" labelRequired="0" model="user.phone" type="number" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="Fax" labelRequired="0" model="user.fax" type="number" />
                        <small class="form-text text-muted">Must be exactly 10 digits, cannot start with 0 or 1</small>
                    </div>
                    <div class="col-md-12 mb-3">
                        <x-form.input.drop-down label="Default Dispatch Method" labelRequired="1" model="user.default_dispatch_method">
                            <option value="">Select Method</option>
                            <option value="{{ User::DISPATCH_METHOD_FAX }}" {{ $user->default_dispatch_method == User::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
                                Fax Plus
                            </option>
                            <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}" {{ $user->default_dispatch_method == User::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
                                Dispense Pro
                            </option>
                        </x-form.input.drop-down>
                    </div>
                    @if ($user['default_dispatch_method'] === User::DISPATCH_METHOD_DISPENSE_PRO)
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="DispensePro abbreviation" labelRequired="1" model="user.dispense_abbreviation" />
                            <small class="form-text text-muted">Required for DispensePro method and must be unique</small>
                        </div>
                    @endif
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="alert-icon">
                        <i class="ki ki-information-circle"></i>
                    </div>
                    <div class="alert-text">
                        <strong>Step 3 of 5:</strong> Configure your contact information and preferred dispatch method. If you select DispensePro, you must provide a unique abbreviation.
                    </div>
                </div>
                
            </x-multipart-form.step-content>

            <!-- Step 4: Address Information -->
            <x-multipart-form.step-content 
                :stepNumber="4" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(4)"
                :description="$this->getStepDescription(4)">
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <x-form.input.text label="Address" labelRequired="0" model="user.address" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.input.text label="City" labelRequired="0" model="user.city" />
                    </div>
                    <div class="col-md-3 mb-3">
                        <x-form.input.drop-down label="State" labelRequired="0" model="user.state_id" placeholder="Select State">
                            <option value="">Select State</option>
                            @foreach ($states as $state)
                                <option value="{{ $state->id }}" {{ $user->state_id == $state->id ? 'selected' : '' }}>
                                    {{ $state->name }}
                                </option>
                            @endforeach
                        </x-form.input.drop-down>
                    </div>
                    <div class="col-md-3 mb-3">
                        <x-form.input.text label="Zip" labelRequired="0" model="user.zip" />
                        <small class="form-text text-muted">Format: 12345 or 12345-6789</small>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="alert-icon">
                        <i class="ki ki-information-circle"></i>
                    </div>
                    <div class="alert-text">
                        <strong>Step 4 of 5:</strong> Enter your address information. All fields are optional, but if you provide a ZIP code, it must be in the correct format (5 digits or 5+4 format).
                    </div>
                </div>
                
            </x-multipart-form.step-content>

            <!-- Step 5: Signature Upload -->
            <x-multipart-form.step-content 
                :stepNumber="5" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(5)"
                :description="$this->getStepDescription(5)">
                
                <div class="row">
                    <div class="col-12" id="signature-container">
                        @if ($user->signature)
                            <div class="mb-3">
                                <p class="mb-0">Current Signature</p>
                                <img class="mb-3 border" style="object-fit: contain; max-width: 200px" src="/storage/{{ $user->signature }}">
                            </div>
                        @endif

                        <x-form.input.image 
                            labelRequired="{{ !$user->id ? '1' : '0' }}" 
                            :preview="$signature" 
                            label="{{ $user->id ? 'Upload New Signature' : 'Upload Signature' }}"
                            :previewUrl="$signature ? optional($signature)->temporaryUrl() : null" 
                            model="signature" />
                            
                        @if(!$user->id)
                            <small class="form-text text-muted">
                                <strong>Required:</strong> Please upload your digital signature. Accepted formats: JPG, JPEG, PNG, GIF, WEBP, BMP. Maximum size: 2MB.
                            </small>
                        @else
                            <small class="form-text text-muted">
                                Upload a new signature to replace the existing one. Accepted formats: JPG, JPEG, PNG, GIF, WEBP, BMP. Maximum size: 2MB.
                            </small>
                        @endif
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="alert-icon">
                        <i class="ki ki-information-circle"></i>
                    </div>
                    <div class="alert-text">
                        <strong>Step 5 of 5:</strong> 
                        @if(!$user->id)
                            Upload your digital signature. This is required for new provider registration and will be used on all prescriptions and documents.
                        @else
                            Upload a new signature to replace your existing one, or leave blank to keep the current signature.
                        @endif
                    </div>
                </div>
                
            </x-multipart-form.step-content>

            <!-- Navigation -->
            <x-multipart-form.navigation 
                :currentStep="$currentStep"
                :totalSteps="$totalSteps"
                :isFirstStep="$this->isFirstStep()"
                :isLastStep="$this->isLastStep()"
                submitAction="submitMultipartForm" />

        </form>
        
    </x-multipart-form.wrapper>

    <!-- Global Errors -->
    <x-group.errors />
</div>

@push('styles')
<style>
    [x-cloak] {
        display: none !important;
    }

    .btn.btn-primary {
        min-width: 120px;
        position: relative;
    }

    .btn.btn-primary span {
        position: relative;
        z-index: 2;
    }

    .btn.disabled,
    .btn[disabled] {
        opacity: 0.65;
        cursor: not-allowed;
        pointer-events: none;
    }

    .multipart-form-step {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/multipart-form.js') }}"></script>
<script>
document.addEventListener("livewire:load", function() {
    // Initialize multipart form with custom options
    const multipartFormOptions = {
        enableKeyboardNavigation: true,
        autoScrollToStep: true,
        validateOnStepChange: true,
        showConfirmOnExit: false
    };

    // Initialize the form
    if (document.getElementById('multipart-form-wizard')) {
        const form = new MultipartForm('multipart-form-wizard', multipartFormOptions);

        // Custom event listeners
        document.getElementById('multipart-form-wizard').addEventListener('multipartForm:stepChanged', function(e) {
            console.log('Step changed to:', e.detail.step);
            // Re-initialize Select2 dropdowns when step changes
            setTimeout(function() {
                initializeSelect2();
            }, 100);
        });
    }

    // Initialize global variable to track upload state
    window.signatureUploading = false;

    // Function to initialize Select2 dropdowns
    function initializeSelect2() {
        // State dropdown
        if ($('#user\\.state_id').length) {
            $('#user\\.state_id').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                @this.set('user.state_id', $(e.target).val());
            });
        }

        // Default Dispatch Method dropdown
        if ($('#user\\.default_dispatch_method').length) {
            $('#user\\.default_dispatch_method').select2({
                placeholder: "Select Method",
                width: '100%'
            }).on('change', function(e) {
                @this.set('user.default_dispatch_method', $(e.target).val());
            });
        }
    }

    // Initialize on load
    initializeSelect2();

    // Re-initialize Select2 after Livewire updates the DOM
    window.livewire.on('contentChanged', function() {
        setTimeout(function() {
            initializeSelect2();
        }, 100);
    });

    // Handle signature upload state
    let signatureUploading = false;

    // Listen for upload events
    window.addEventListener('livewire-upload-start', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            signatureUploading = true;
        }
    });

    window.addEventListener('livewire-upload-finish', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            setTimeout(function() {
                signatureUploading = false;
            }, 500);
        }
    });

    window.addEventListener('livewire-upload-error', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            signatureUploading = false;
        }
    });
});
</script>
@endpush
