@props([
    'stepNumber' => 1,
    'currentStep' => 1,
    'title' => '',
    'description' => ''
])

<div class="wizard-content" 
     data-wizard-type="step-content" 
     data-wizard-state="{{ $stepNumber == $currentStep ? 'current' : '' }}"
     style="{{ $stepNumber == $currentStep ? 'display: block;' : 'display: none;' }}">
    
    <x-card>
        <x-card.body>
            @if($title || $description)
                <div class="mb-6">
                    @if($title)
                        <h3 class="mb-3">{{ $title }}</h3>
                    @endif
                    @if($description)
                        <p class="text-muted">{{ $description }}</p>
                    @endif
                </div>
            @endif
            
            {{ $slot }}
        </x-card.body>
    </x-card>
    
</div>
