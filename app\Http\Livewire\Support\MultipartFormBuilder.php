<?php

namespace App\Http\Livewire\Support;

class MultipartFormBuilder
{
    protected $config = [
        'title' => 'Multi-Step Form',
        'showProgressBar' => true,
        'allowStepNavigation' => false,
        'steps' => []
    ];

    /**
     * Create a new form builder instance
     */
    public static function create($title = 'Multi-Step Form')
    {
        $builder = new static();
        $builder->config['title'] = $title;
        return $builder;
    }

    /**
     * Set form title
     */
    public function title($title)
    {
        $this->config['title'] = $title;
        return $this;
    }

    /**
     * Show or hide progress bar
     */
    public function showProgressBar($show = true)
    {
        $this->config['showProgressBar'] = $show;
        return $this;
    }

    /**
     * Allow or disallow step navigation
     */
    public function allowStepNavigation($allow = true)
    {
        $this->config['allowStepNavigation'] = $allow;
        return $this;
    }

    /**
     * Add a step to the form
     */
    public function addStep($title, $description = '', $view = null)
    {
        $step = new MultipartFormStep($title, $description, $view);
        $this->config['steps'][] = $step;
        return $step;
    }

    /**
     * Get the configuration array
     */
    public function build()
    {
        // Convert step objects to arrays
        $steps = [];
        foreach ($this->config['steps'] as $step) {
            $steps[] = $step->toArray();
        }
        
        $this->config['steps'] = $steps;
        return $this->config;
    }
}

class MultipartFormStep
{
    protected $config = [
        'title' => '',
        'description' => '',
        'view' => null,
        'fields' => [],
        'rules' => [],
        'messages' => []
    ];

    public function __construct($title, $description = '', $view = null)
    {
        $this->config['title'] = $title;
        $this->config['description'] = $description;
        $this->config['view'] = $view;
    }

    /**
     * Set step title
     */
    public function title($title)
    {
        $this->config['title'] = $title;
        return $this;
    }

    /**
     * Set step description
     */
    public function description($description)
    {
        $this->config['description'] = $description;
        return $this;
    }

    /**
     * Set step view
     */
    public function view($view)
    {
        $this->config['view'] = $view;
        return $this;
    }

    /**
     * Add fields to this step
     */
    public function fields(array $fields)
    {
        $this->config['fields'] = array_merge($this->config['fields'], $fields);
        return $this;
    }

    /**
     * Add a single field to this step
     */
    public function field($field)
    {
        $this->config['fields'][] = $field;
        return $this;
    }

    /**
     * Add validation rules for this step
     */
    public function rules(array $rules)
    {
        $this->config['rules'] = array_merge($this->config['rules'], $rules);
        return $this;
    }

    /**
     * Add a single validation rule
     */
    public function rule($field, $rule)
    {
        $this->config['rules'][$field] = $rule;
        return $this;
    }

    /**
     * Add validation messages for this step
     */
    public function messages(array $messages)
    {
        $this->config['messages'] = array_merge($this->config['messages'], $messages);
        return $this;
    }

    /**
     * Add a single validation message
     */
    public function message($field, $message)
    {
        $this->config['messages'][$field] = $message;
        return $this;
    }

    /**
     * Convert step to array
     */
    public function toArray()
    {
        return $this->config;
    }
}

/**
 * Helper function to create a multipart form builder
 */
function multipartForm($title = 'Multi-Step Form')
{
    return MultipartFormBuilder::create($title);
}
