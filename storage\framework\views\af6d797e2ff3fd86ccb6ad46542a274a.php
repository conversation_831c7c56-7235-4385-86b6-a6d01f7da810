<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'title' => 'Multi-Step Form',
    'showProgressBar' => true,
    'allowStepNavigation' => false,
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => []
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'title' => 'Multi-Step Form',
    'showProgressBar' => true,
    'allowStepNavigation' => false,
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => []
]); ?>
<?php foreach (array_filter(([
    'title' => 'Multi-Step Form',
    'showProgressBar' => true,
    'allowStepNavigation' => false,
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => []
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="wizard wizard-4"
     id="multipart-form-wizard"
     data-wizard-state="<?php echo e($currentStep === 1 ? 'first' : ($currentStep === $totalSteps ? 'last' : 'between')); ?>"
     data-wizard-clickable="<?php echo e($allowStepNavigation ? 'true' : 'false'); ?>">
    
    <?php if($showProgressBar && $totalSteps > 1): ?>
        <?php if (isset($component)) { $__componentOriginale77fe2ef3ae01674176c0817b12ce1bb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale77fe2ef3ae01674176c0817b12ce1bb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.multipart-form.progress-bar','data' => ['currentStep' => $currentStep,'totalSteps' => $totalSteps,'steps' => $steps,'allowStepNavigation' => $allowStepNavigation]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('multipart-form.progress-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'totalSteps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalSteps),'steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'allowStepNavigation' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowStepNavigation)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale77fe2ef3ae01674176c0817b12ce1bb)): ?>
<?php $attributes = $__attributesOriginale77fe2ef3ae01674176c0817b12ce1bb; ?>
<?php unset($__attributesOriginale77fe2ef3ae01674176c0817b12ce1bb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale77fe2ef3ae01674176c0817b12ce1bb)): ?>
<?php $component = $__componentOriginale77fe2ef3ae01674176c0817b12ce1bb; ?>
<?php unset($__componentOriginale77fe2ef3ae01674176c0817b12ce1bb); ?>
<?php endif; ?>
    <?php endif; ?>

    <div class="wizard-body">
        <?php echo e($slot); ?>

    </div>

</div>

<?php $__env->startPush('styles'); ?>
<style>
    /* Multipart Form Wizard Styles - Based on Metronic Wizard-4 */
    .wizard.wizard-4 {
        background: #fff;
        border-radius: 0.42rem;
        box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
        padding: 2rem;
    }

    .wizard.wizard-4 .wizard-nav {
        padding: 0;
        margin-bottom: 2rem;
        border: 0;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        background-color: #f7f8fa;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
        transition: all 0.3s ease;
        margin-bottom: 0.25rem;
        border: 1px solid #ebedf3;
        border-bottom: none;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
        background-color: #f3f6f9;
        border-color: #3699ff;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] {
        background-color: #e8f5e8;
        border-color: #1bc5bd;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        color: #b5b5c3;
        padding: 2rem 1.5rem;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper {
        color: #3699ff;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-wrapper {
        color: #1bc5bd;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-number {
        font-size: 1.3rem;
        font-weight: 600;
        flex: 0 0 2.75rem;
        height: 2.75rem;
        width: 2.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(181, 181, 195, 0.08);
        color: #b5b5c3;
        margin-right: 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-number {
        background-color: rgba(54, 153, 255, 0.08);
        color: #3699ff;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-wrapper .wizard-number {
        background-color: rgba(27, 197, 189, 0.08);
        color: #1bc5bd;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #b5b5c3;
        margin-bottom: 0.25rem;
        transition: all 0.3s ease;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-label .wizard-title {
        color: #181c32;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-wrapper .wizard-label .wizard-title {
        color: #181c32;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-desc {
        color: #b5b5c3;
        font-size: 0.9rem;
        line-height: 1.4;
        transition: all 0.3s ease;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-wrapper .wizard-label .wizard-desc {
        color: #7e8299;
    }

    .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-wrapper .wizard-label .wizard-desc {
        color: #7e8299;
    }

    /* Error states */
    .wizard-step-error {
        background-color: #ffe2e5 !important;
        border-color: #f64e60 !important;
    }

    .wizard-step-error .wizard-wrapper {
        color: #f64e60 !important;
    }

    .wizard-step-error .wizard-number {
        background-color: #f64e60 !important;
        color: white !important;
    }

    .wizard-step-error .wizard-title {
        color: #f64e60 !important;
    }

    .wizard-step-error .wizard-desc {
        color: #f64e60 !important;
    }

    /* Wizard body and actions */
    .wizard-body {
        min-height: 300px;
        padding: 2rem 0;
    }

    .wizard-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #ebedf3;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Hover effects for clickable steps */
    .wizard[data-wizard-clickable="true"] .wizard-step:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
            flex: 0 0 calc(50% - 0.125rem);
            width: calc(50% - 0.125rem);
        }
    }

    @media (max-width: 768px) {
        .wizard.wizard-4 {
            padding: 1.5rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step {
            flex: 0 0 100%;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
            padding: 1.5rem 1rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-number {
            flex: 0 0 2rem;
            height: 2rem;
            width: 2rem;
            font-size: 1.1rem;
            margin-right: 0.75rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-title {
            font-size: 1rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-desc {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 576px) {
        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
            padding: 1rem 0.75rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-number {
            flex: 0 0 1.75rem;
            height: 1.75rem;
            width: 1.75rem;
            font-size: 1rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-title {
            font-size: 0.9rem;
        }

        .wizard.wizard-4 .wizard-nav .wizard-steps .wizard-step .wizard-wrapper .wizard-label .wizard-desc {
            font-size: 0.75rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/multipart-form/wrapper.blade.php ENDPATH**/ ?>