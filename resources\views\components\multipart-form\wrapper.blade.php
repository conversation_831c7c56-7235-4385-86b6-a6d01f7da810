@props([
    'title' => 'Multi-Step Form',
    'showProgressBar' => true,
    'allowStepNavigation' => false,
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => []
])

<div class="wizard wizard-1" 
     id="multipart-form-wizard" 
     data-wizard-state="{{ $currentStep === 1 ? 'first' : ($currentStep === $totalSteps ? 'last' : 'between') }}" 
     data-wizard-clickable="{{ $allowStepNavigation ? 'true' : 'false' }}">
    
    @if($showProgressBar && $totalSteps > 1)
        <x-multipart-form.progress-bar 
            :currentStep="$currentStep" 
            :totalSteps="$totalSteps" 
            :steps="$steps"
            :allowStepNavigation="$allowStepNavigation" />
    @endif

    <div class="wizard-body">
        {{ $slot }}
    </div>

    @if($showProgressBar && $totalSteps > 1)
        <x-multipart-form.progress-info 
            :currentStep="$currentStep" 
            :totalSteps="$totalSteps" />
    @endif

</div>

@push('styles')
<style>
    .wizard-step {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .wizard-step:not([data-wizard-clickable="true"]) {
        cursor: default;
    }
    
    .wizard-step-error .wizard-number {
        background-color: #f64e60 !important;
        color: white !important;
    }
    
    .wizard-step-error .wizard-title {
        color: #f64e60 !important;
    }
    
    .wizard-content {
        min-height: 300px;
    }
    
    .wizard-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #ebedf3;
    }
    
    .multipart-form-wrapper {
        background: #fff;
        border-radius: 0.42rem;
        box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    }
</style>
@endpush
