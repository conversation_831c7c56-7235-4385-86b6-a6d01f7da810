<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label class="form-label {{ !$user->id ? 'required' : '' }}">Digital Signature</label>
            
            <div class="custom-file">
                <input type="file" 
                       class="custom-file-input" 
                       id="signature" 
                       wire:model="signature"
                       accept="image/*">
                <label class="custom-file-label" for="signature">
                    @if($signature)
                        {{ $signature->getClientOriginalName() }}
                    @else
                        Choose signature file...
                    @endif
                </label>
            </div>
            
            @if(!$user->id)
                <small class="form-text text-muted">
                    <strong>Required:</strong> Please upload your digital signature. Accepted formats: JPG, JPEG, PNG, GIF, WEBP, BMP. Maximum size: 2MB.
                </small>
            @else
                <small class="form-text text-muted">
                    Upload a new signature to replace the existing one. Accepted formats: JPG, JPEG, PNG, GIF, WEBP, BMP. Maximum size: 2MB.
                </small>
            @endif
            
            @error('signature')
                <div class="invalid-feedback d-block">{{ $message }}</div>
            @enderror
        </div>

        <!-- Upload Progress -->
        <div wire:loading wire:target="signature" class="mt-3">
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" 
                     style="width: 100%">
                    Uploading signature...
                </div>
            </div>
        </div>

        <!-- Current Signature Preview -->
        @if($user->signature && !$signature)
            <div class="mt-4">
                <label class="form-label">Current Signature:</label>
                <div class="signature-preview">
                    <img src="{{ Storage::url($user->signature) }}" 
                         alt="Current Signature" 
                         class="img-fluid border rounded p-2"
                         style="max-height: 150px; max-width: 300px;">
                </div>
            </div>
        @endif

        <!-- New Signature Preview -->
        @if($signature)
            <div class="mt-4">
                <label class="form-label">New Signature Preview:</label>
                <div class="signature-preview">
                    <img src="{{ $signature->temporaryUrl() }}" 
                         alt="New Signature" 
                         class="img-fluid border rounded p-2"
                         style="max-height: 150px; max-width: 300px;">
                </div>
            </div>
        @endif
    </div>
</div>

<div class="alert alert-info mt-4">
    <div class="alert-icon">
        <i class="ki ki-information-circle"></i>
    </div>
    <div class="alert-text">
        <strong>Step 5 of 5:</strong> 
        @if(!$user->id)
            Upload your digital signature. This is required for new provider registration and will be used on all prescriptions and documents.
        @else
            Upload a new signature to replace your existing one, or leave blank to keep the current signature.
        @endif
    </div>
</div>

@push('styles')
<style>
    .signature-preview {
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        text-align: center;
    }
    
    .signature-preview img {
        background-color: white;
    }
    
    .custom-file-label::after {
        content: "Browse";
    }
    
    .progress {
        height: 6px;
    }
</style>
@endpush
