# Multipart Form System Documentation

## Overview

The Multipart Form System is a comprehensive solution for creating step-by-step forms in Laravel Livewire applications. It provides a reusable, flexible way to break down complex forms into manageable steps with validation, progress tracking, and navigation.

## Features

- **Step-by-step navigation** with validation per step
- **Progress tracking** with visual progress bar
- **Flexible configuration** using a fluent builder pattern
- **Reusable Blade components** for consistent UI
- **JavaScript integration** with keyboard navigation and smooth transitions
- **Validation per step** with error handling
- **Customizable styling** that integrates with Metronic theme
- **Event-driven architecture** for extensibility

## Quick Start

### 1. Create a Livewire Component

```php
<?php

namespace App\Http\Livewire\Example;

use App\Http\Livewire\Traits\HasMultipartForm;
use App\Http\Livewire\Support\MultipartFormBuilder;
use Livewire\Component;

class ExampleForm extends Component
{
    use HasMultipartForm;

    public $name;
    public $email;
    public $phone;

    public function mount()
    {
        $this->initializeMultipartForm();
    }

    protected function getMultipartConfig()
    {
        return MultipartFormBuilder::create('Example Form')
            ->showProgressBar(true)
            ->allowStepNavigation(false)
            ->addStep('Personal Info', 'Enter your personal information')
                ->fields(['name', 'email'])
                ->rules([
                    'name' => 'required|max:255',
                    'email' => 'required|email'
                ])
            ->addStep('Contact Info', 'Enter your contact information')
                ->fields(['phone'])
                ->rules([
                    'phone' => 'required|max:15'
                ])
            ->build();
    }

    public function store()
    {
        // Handle form submission
        // This method is called when the form is submitted
    }

    public function render()
    {
        return view('livewire.example.form');
    }
}
```

### 2. Create the Blade Template

```blade
<div>
    <x-multipart-form.wrapper 
        :title="$multipartConfig['title']"
        :showProgressBar="$multipartConfig['showProgressBar']"
        :allowStepNavigation="$multipartConfig['allowStepNavigation']"
        :currentStep="$currentStep"
        :totalSteps="$totalSteps"
        :steps="$multipartConfig['steps']">
        
        <form wire:submit.prevent="submitMultipartForm">
            
            <!-- Step 1: Personal Info -->
            <x-multipart-form.step-content 
                :stepNumber="1" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(1)"
                :description="$this->getStepDescription(1)">
                
                <x-form.input wire:model.defer="name" name="name" label="Name" required="true" />
                <x-form.input wire:model.defer="email" name="email" label="Email" type="email" required="true" />
                
            </x-multipart-form.step-content>

            <!-- Step 2: Contact Info -->
            <x-multipart-form.step-content 
                :stepNumber="2" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(2)"
                :description="$this->getStepDescription(2)">
                
                <x-form.input wire:model.defer="phone" name="phone" label="Phone" required="true" />
                
            </x-multipart-form.step-content>

            <!-- Navigation -->
            <x-multipart-form.navigation 
                :currentStep="$currentStep"
                :totalSteps="$totalSteps"
                :isFirstStep="$this->isFirstStep()"
                :isLastStep="$this->isLastStep()"
                submitAction="submitMultipartForm" />

        </form>
        
    </x-multipart-form.wrapper>
</div>
```

## Core Components

### 1. HasMultipartForm Trait

The `HasMultipartForm` trait provides all the functionality needed for multipart forms:

- **Step navigation**: `goToNextStep()`, `goToPrevStep()`, `goToStep($number)`
- **Validation**: `validateCurrentStep()`, `validateAllSteps()`
- **State management**: `isFirstStep()`, `isLastStep()`, `isStepCompleted($step)`
- **Form submission**: `submitMultipartForm()`

### 2. MultipartFormBuilder

The builder class provides a fluent API for configuring forms:

```php
MultipartFormBuilder::create('Form Title')
    ->showProgressBar(true)
    ->allowStepNavigation(false)
    ->addStep('Step Title', 'Step Description')
        ->fields(['field1', 'field2'])
        ->rules(['field1' => 'required'])
        ->messages(['field1.required' => 'Custom message'])
    ->build();
```

### 3. Blade Components

#### Wrapper Component
```blade
<x-multipart-form.wrapper 
    title="Form Title"
    :showProgressBar="true"
    :allowStepNavigation="false"
    :currentStep="$currentStep"
    :totalSteps="$totalSteps"
    :steps="$steps">
    <!-- Form content -->
</x-multipart-form.wrapper>
```

#### Step Content Component
```blade
<x-multipart-form.step-content 
    :stepNumber="1" 
    :currentStep="$currentStep"
    title="Step Title"
    description="Step Description">
    <!-- Step fields -->
</x-multipart-form.step-content>
```

#### Navigation Component
```blade
<x-multipart-form.navigation 
    :currentStep="$currentStep"
    :totalSteps="$totalSteps"
    :isFirstStep="$isFirstStep"
    :isLastStep="$isLastStep"
    submitAction="submitMultipartForm" />
```

#### Progress Bar Component
```blade
<x-multipart-form.progress-bar 
    :currentStep="$currentStep" 
    :totalSteps="$totalSteps" 
    :steps="$steps"
    :allowStepNavigation="$allowStepNavigation" />
```

## Configuration Options

### Form Builder Options

- `title(string)`: Set the form title
- `showProgressBar(bool)`: Show/hide the progress bar
- `allowStepNavigation(bool)`: Allow clicking on steps to navigate

### Step Configuration

- `title(string)`: Step title
- `description(string)`: Step description
- `view(string)`: Custom view for the step
- `fields(array)`: Fields included in this step
- `rules(array)`: Validation rules for this step
- `messages(array)`: Custom validation messages

## Advanced Usage

### Custom Step Views

You can specify custom views for individual steps:

```php
->addStep('Custom Step', 'Description')
    ->view('custom.step-view')
    ->fields(['field1'])
    ->rules(['field1' => 'required'])
```

### Conditional Fields

Handle conditional fields by updating validation rules dynamically:

```php
public function rules()
{
    $rules = $this->getAllValidationRules();
    
    // Add conditional rules
    if ($this->someCondition) {
        $rules['conditionalField'] = 'required';
    }
    
    return $rules;
}
```

### Custom Navigation

Override navigation methods for custom behavior:

```php
public function goToNextStep()
{
    // Custom logic before going to next step
    if ($this->customValidation()) {
        parent::goToNextStep();
    }
}
```

## JavaScript Integration

The system includes JavaScript for enhanced user experience:

- **Keyboard navigation**: Arrow keys for step navigation
- **Auto-scroll**: Automatically scroll to current step
- **Form validation**: Client-side validation integration
- **Event handling**: Custom events for step changes

### JavaScript Events

Listen for custom events:

```javascript
document.getElementById('multipart-form-wizard').addEventListener('multipartForm:stepChanged', function(e) {
    console.log('Step changed to:', e.detail.step);
});
```

## Styling

The system uses Metronic theme classes and includes custom CSS for:

- Step indicators with completion states
- Progress bars with animations
- Error states for invalid steps
- Responsive design for mobile devices

## Best Practices

1. **Keep steps focused**: Each step should have a clear purpose
2. **Validate per step**: Don't wait until the end to validate
3. **Provide clear feedback**: Use descriptions and help text
4. **Handle errors gracefully**: Show clear error messages
5. **Test navigation**: Ensure all navigation paths work correctly

## Troubleshooting

### Common Issues

1. **Validation not working**: Ensure `rules()` method returns all validation rules
2. **Steps not showing**: Check that step numbers match the configuration
3. **JavaScript errors**: Ensure the JavaScript file is included
4. **Styling issues**: Verify Metronic theme CSS is loaded

### Debug Mode

Enable debug mode to see detailed information:

```php
// In your component
public function mount()
{
    $this->initializeMultipartForm();
    
    if (config('app.debug')) {
        logger('Multipart form config:', $this->multipartConfig);
    }
}
```

## Examples

See the complete example implementation in:
- `app/Http/Livewire/User/CreateEditMultipart.php`
- `resources/views/livewire/user/create-edit-multipart.blade.php`
- `resources/views/livewire/user/multipart/` directory for step templates

This example shows how to convert a complex user registration form into a 5-step multipart form with validation, file uploads, and conditional fields.
