@php
    use App\Models\User;
@endphp

<div>
    <x-multipart-form.wrapper 
        :title="$multipartConfig['title']"
        :showProgressBar="$multipartConfig['showProgressBar']"
        :allowStepNavigation="$multipartConfig['allowStepNavigation']"
        :currentStep="$currentStep"
        :totalSteps="$totalSteps"
        :steps="$multipartConfig['steps']">
        
        <form wire:submit.prevent="submitMultipartForm">
            
            <!-- Step 1: Basic Information -->
            <x-multipart-form.step-content 
                :stepNumber="1" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(1)"
                :description="$this->getStepDescription(1)">
                @include('livewire.user.multipart.step-basic-info')
            </x-multipart-form.step-content>

            <!-- Step 2: Professional Information -->
            <x-multipart-form.step-content 
                :stepNumber="2" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(2)"
                :description="$this->getStepDescription(2)">
                @include('livewire.user.multipart.step-professional-info')
            </x-multipart-form.step-content>

            <!-- Step 3: Contact & Dispatch -->
            <x-multipart-form.step-content 
                :stepNumber="3" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(3)"
                :description="$this->getStepDescription(3)">
                @include('livewire.user.multipart.step-contact-dispatch')
            </x-multipart-form.step-content>

            <!-- Step 4: Address Information -->
            <x-multipart-form.step-content 
                :stepNumber="4" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(4)"
                :description="$this->getStepDescription(4)">
                @include('livewire.user.multipart.step-address')
            </x-multipart-form.step-content>

            <!-- Step 5: Signature Upload -->
            <x-multipart-form.step-content 
                :stepNumber="5" 
                :currentStep="$currentStep"
                :title="$this->getStepTitle(5)"
                :description="$this->getStepDescription(5)">
                @include('livewire.user.multipart.step-signature')
            </x-multipart-form.step-content>

            <!-- Navigation -->
            <x-multipart-form.navigation 
                :currentStep="$currentStep"
                :totalSteps="$totalSteps"
                :isFirstStep="$this->isFirstStep()"
                :isLastStep="$this->isLastStep()"
                submitAction="submitMultipartForm" />

        </form>
        
    </x-multipart-form.wrapper>

    <!-- Global Errors -->
    <x-group.errors />
</div>

@push('styles')
<style>
    [x-cloak] {
        display: none !important;
    }

    .btn.btn-primary {
        min-width: 120px;
        position: relative;
    }

    .btn.btn-primary span {
        position: relative;
        z-index: 2;
    }

    .btn.disabled,
    .btn[disabled] {
        opacity: 0.65;
        cursor: not-allowed;
        pointer-events: none;
    }

    .multipart-form-step {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener("livewire:load", function() {
    // Initialize Select2 dropdowns for each step
    function initializeSelect2() {
        // State dropdown
        if ($('#user\\.state_id').length) {
            $('#user\\.state_id').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                @this.set('user.state_id', $(e.target).val());
            });
        }

        // Default Dispatch Method dropdown
        if ($('#user\\.default_dispatch_method').length) {
            $('#user\\.default_dispatch_method').select2({
                placeholder: "Select Method",
                width: '100%'
            }).on('change', function(e) {
                @this.set('user.default_dispatch_method', $(e.target).val());
            });
        }
    }

    // Initialize on load
    initializeSelect2();

    // Re-initialize when step changes
    window.livewire.on('stepChanged', function() {
        setTimeout(function() {
            initializeSelect2();
        }, 100);
    });

    // Re-initialize Select2 after Livewire updates the DOM
    window.livewire.on('contentChanged', function() {
        setTimeout(function() {
            initializeSelect2();
        }, 100);
    });

    // Handle signature upload state
    let signatureUploading = false;
    
    // Listen for upload events
    window.addEventListener('livewire-upload-start', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            signatureUploading = true;
        }
    });

    window.addEventListener('livewire-upload-finish', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            setTimeout(function() {
                signatureUploading = false;
            }, 500);
        }
    });

    window.addEventListener('livewire-upload-error', function(event) {
        if (event.detail && event.detail.name === 'signature') {
            signatureUploading = false;
        }
    });
});
</script>
@endpush
