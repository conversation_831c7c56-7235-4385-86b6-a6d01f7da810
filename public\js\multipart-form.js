/**
 * Multipart Form JavaScript Integration
 * Integrates with existing Metronic wizard functionality and Livewire
 */

class MultipartForm {
    constructor(elementId, options = {}) {
        this.elementId = elementId;
        this.element = document.getElementById(elementId);
        this.options = {
            enableKeyboardNavigation: true,
            autoScrollToStep: true,
            validateOnStepChange: true,
            showConfirmOnExit: false,
            ...options
        };
        
        this.currentStep = 1;
        this.totalSteps = 1;
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        if (!this.element) {
            console.error(`Multipart form element with ID '${this.elementId}' not found`);
            return;
        }

        this.setupEventListeners();
        this.setupKeyboardNavigation();
        this.setupFormValidation();
        this.isInitialized = true;
        
        console.log('Multipart form initialized:', this.elementId);
    }

    setupEventListeners() {
        // Listen for Livewire step changes
        if (window.livewire) {
            window.livewire.on('stepChanged', (step) => {
                this.onStepChanged(step);
            });

            window.livewire.on('formSubmitted', (data) => {
                this.onFormSubmitted(data);
            });

            window.livewire.on('formValidationFailed', (errors) => {
                this.onFormValidationFailed(errors);
            });

            window.livewire.on('formReset', () => {
                this.onFormReset();
            });
        }

        // Setup navigation button listeners
        this.setupNavigationButtons();
        
        // Setup step click listeners if navigation is allowed
        this.setupStepClickListeners();
    }

    setupNavigationButtons() {
        const prevButtons = this.element.querySelectorAll('[data-wizard-type="action-prev"]');
        const nextButtons = this.element.querySelectorAll('[data-wizard-type="action-next"]');
        const submitButtons = this.element.querySelectorAll('[data-wizard-type="action-submit"]');

        prevButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToPrevStep();
            });
        });

        nextButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToNextStep();
            });
        });

        submitButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.submitForm();
            });
        });
    }

    setupStepClickListeners() {
        const stepElements = this.element.querySelectorAll('.wizard-step[data-wizard-clickable="true"]');
        
        stepElements.forEach((stepElement, index) => {
            stepElement.addEventListener('click', (e) => {
                e.preventDefault();
                const stepNumber = index + 1;
                this.goToStep(stepNumber);
            });
        });
    }

    setupKeyboardNavigation() {
        if (!this.options.enableKeyboardNavigation) return;

        document.addEventListener('keydown', (e) => {
            // Only handle keyboard navigation when form is focused
            if (!this.element.contains(document.activeElement)) return;

            switch (e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    this.goToPrevStep();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    this.goToNextStep();
                    break;
                case 'Enter':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.submitForm();
                    }
                    break;
                case 'Escape':
                    if (this.options.showConfirmOnExit) {
                        this.confirmExit();
                    }
                    break;
            }
        });
    }

    setupFormValidation() {
        if (!this.options.validateOnStepChange) return;

        // Add validation to form inputs
        const formInputs = this.element.querySelectorAll('input, select, textarea');
        
        formInputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }

    onStepChanged(step) {
        this.currentStep = step;
        this.updateWizardState();
        
        if (this.options.autoScrollToStep) {
            this.scrollToStep();
        }
        
        // Update URL hash if needed
        this.updateUrlHash(step);
        
        // Trigger custom event
        this.triggerEvent('stepChanged', { step });
    }

    onFormSubmitted(data) {
        this.triggerEvent('formSubmitted', { data });
        
        // Show success message
        this.showSuccessMessage('Form submitted successfully!');
    }

    onFormValidationFailed(errors) {
        this.triggerEvent('formValidationFailed', { errors });
        
        // Show error message
        this.showErrorMessage('Please fix the errors in the form before submitting.');
    }

    onFormReset() {
        this.currentStep = 1;
        this.updateWizardState();
        this.triggerEvent('formReset');
    }

    goToNextStep() {
        if (window.livewire) {
            window.livewire.emit('nextStep');
        }
    }

    goToPrevStep() {
        if (window.livewire) {
            window.livewire.emit('prevStep');
        }
    }

    goToStep(stepNumber) {
        if (window.livewire) {
            window.livewire.emit('goToStep', stepNumber);
        }
    }

    submitForm() {
        if (window.livewire) {
            window.livewire.emit('submitForm');
        }
    }

    validateCurrentStep() {
        if (window.livewire) {
            window.livewire.emit('validateCurrentStep');
        }
    }

    validateField(field) {
        // Custom field validation logic can be added here
        // This integrates with Livewire's validation
    }

    updateWizardState() {
        if (!this.element) return;

        const totalSteps = this.element.querySelectorAll('.wizard-step').length;
        this.totalSteps = totalSteps;

        // Update wizard state attribute
        let state = 'between';
        if (this.currentStep === 1) state = 'first';
        else if (this.currentStep === totalSteps) state = 'last';
        
        this.element.setAttribute('data-wizard-state', state);

        // Update step visibility
        this.updateStepVisibility();
    }

    updateStepVisibility() {
        const stepContents = this.element.querySelectorAll('[data-wizard-type="step-content"]');
        
        stepContents.forEach((content, index) => {
            const stepNumber = index + 1;
            if (stepNumber === this.currentStep) {
                content.style.display = 'block';
                content.setAttribute('data-wizard-state', 'current');
            } else {
                content.style.display = 'none';
                content.setAttribute('data-wizard-state', '');
            }
        });
    }

    scrollToStep() {
        if (this.element) {
            this.element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
    }

    updateUrlHash(step) {
        if (this.options.updateUrl) {
            window.history.replaceState(null, null, `#step-${step}`);
        }
    }

    confirmExit() {
        if (confirm('Are you sure you want to exit? Your progress may be lost.')) {
            // Handle exit logic
            this.triggerEvent('formExit');
        }
    }

    showSuccessMessage(message) {
        // Integration with SweetAlert if available
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }

    showErrorMessage(message) {
        // Integration with SweetAlert if available
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: message
            });
        } else {
            alert(message);
        }
    }

    triggerEvent(eventName, data = {}) {
        const event = new CustomEvent(`multipartForm:${eventName}`, {
            detail: { ...data, form: this }
        });
        this.element.dispatchEvent(event);
    }

    // Public API methods
    getCurrentStep() {
        return this.currentStep;
    }

    getTotalSteps() {
        return this.totalSteps;
    }

    getProgressPercentage() {
        return Math.round((this.currentStep / this.totalSteps) * 100);
    }

    destroy() {
        // Cleanup event listeners
        this.isInitialized = false;
    }
}

// Auto-initialize multipart forms
document.addEventListener('DOMContentLoaded', function() {
    const multipartForms = document.querySelectorAll('.wizard[id*="multipart-form"]');
    
    multipartForms.forEach(form => {
        if (form.id) {
            new MultipartForm(form.id);
        }
    });
});

// Export for manual initialization
window.MultipartForm = MultipartForm;
