@props([
    'currentStep' => 1,
    'totalSteps' => 1
])

@php
    $progressPercentage = round(($currentStep / $totalSteps) * 100);
@endphp

<div class="wizard-info mt-4">
    <div class="progress">
        <div class="progress-bar bg-primary" 
             role="progressbar" 
             style="width: {{ $progressPercentage }}%"
             aria-valuenow="{{ $progressPercentage }}" 
             aria-valuemin="0" 
             aria-valuemax="100">
            {{ $progressPercentage }}%
        </div>
    </div>
    <div class="text-center mt-2">
        <small class="text-muted">Step {{ $currentStep }} of {{ $totalSteps }}</small>
    </div>
</div>
