

<?php
use App\Models\User;
use App\Models\ImportFile;

?>

<?php $__env->startSection('content'); ?>
<div class="card card-custom mb-5">

    <div class="card-body" x-data="{ showFilter: false }">

        <div class="row justify-content-between ">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                <div class="input-icon">
                    <input type="text" class="form-control" placeholder="Search..." id="void_search" />
                    <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                    </span>
                </div>
            </div>
            <div class="col-auto">
                <button type="button" id="download-all-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download All
                </button>
                <button type="button" id="download-selected-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download Selected
                </button>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <label for="sent_date_filter">Sent At:</label>
                <div class="input-group date">
                    <input type="date" class="form-control" id="sent_date_filter" max="<?php echo e(date('Y-m-d')); ?>" />
                    <div class="input-group-append">
                        <button class="btn btn-secondary" type="button" id="clear_date_filter">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <?php if(Auth::user()->role !== User::ROLE_PROVIDER): ?>
            <div class="col-md-3 mb-3">
                <label for="provider_filter">Provider:</label>
                <select class="form-control" id="provider_filter">
                    <option value="">All Providers</option>
                    <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($provider->id); ?>"><?php echo e($provider->first_name); ?> <?php echo e($provider->last_name); ?>

                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <?php endif; ?>
            <?php if(Auth::user()->role !== User::ROLE_PROVIDER): ?>
            <div class="col-md-3 mb-3">
                <label for="clinic_name_filter">Clinic:</label>
                <select class="form-control" id="clinic_name_filter">
                    <option value="">All Clinics</option>
                    <?php if(isset($clinic_names)): ?>
                    <?php $__currentLoopData = $clinic_names; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clinic): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($clinic); ?>"><?php echo e($clinic); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </select>
            </div>
            <?php endif; ?>



            <div class="col-md-3 mb-3">
                <label for="medication_filter">Medication:</label>
                <select class="form-control" id="medication_filter">
                    <option value="">All Medications</option>
                    <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($medication->id); ?>"><?php echo e($medication->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>

        <div class="datatable datatable-bordered datatable-head-custom" id="void_dt"></div>

    </div>
</div>

<!-- Script Preview Modal -->
<div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="script-preview-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="download-preview-btn" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<?php echo \Illuminate\View\Factory::parentPlaceholder('styles'); ?>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    button:disabled {
        cursor: not-allowed !important;
    }

    .text-primary {
        text-decoration: none;
    }

    .text-primary:hover {
        text-decoration: underline;
    }

    /* Custom styles for the preview modal */
    #scriptPreviewModal .modal-dialog {
        max-width: 95%;
        height: 95vh;
        margin: 0.5rem auto;
    }

    #scriptPreviewModal .modal-content {
        height: 100%;
        border-radius: 4px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
    }

    #scriptPreviewModal .modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    #scriptPreviewModal .modal-header {
        border-bottom: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
    }

    #scriptPreviewModal .modal-footer {
        border-top: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
        position: relative;
        flex-shrink: 0;
        justify-content: flex-end;
        background-color: #fff;
        z-index: 5;
    }

    #scriptPreviewModal .close {
        cursor: pointer;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        margin: 0;
        padding: 0;
    }

    #scriptPreviewModal .close i {
        font-size: 1rem;
    }

    #scriptPreviewModal .close:hover {
        color: #3699FF;
        background-color: #f3f6f9;
        border-radius: 4px;
    }

    #script-preview-content {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
    }

    #script-preview-content iframe {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
    }

    .select2-container .select2-selection--single {
        height: 38px;
        display: flex;
        align-items: center;
        padding: 6px 12px;
    }

    .select2-container .select2-selection--single .select2-selection__arrow {
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 8px;
    }

    /* Simple style for clickable rows */
    #void_dt tbody tr td:not(:last-child) {
        cursor: pointer;
    }

    /* Highlight on hover */
    #void_dt tbody tr:hover td:not(:last-child) {
        background-color: rgba(54, 153, 255, 0.1) !important;
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#provider_filter').select2({
            width: '100%'
        });
        $('#clinic_name_filter').select2({
            width: '100%'
        });
        $('#medication_filter').select2({
            width: '100%'
        });
    });
</script>
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
<script>
    var datatable;
    var datatableElement;
    var searchElement;
    var columnArray;

    // Store selected IDs globally
    let globalSelectedIds = [];

    const storagePath = `<?php echo e(url('/storage')); ?>`;
    const apiRoute = `<?php echo e(route('scripts.api.voided')); ?>`;
    let url = "<?php echo e(Storage::url('/')); ?>";
    const userEditRoute = `<?php echo e(route('users.edit', ['user' => '::ID'])); ?>`;
    const voidRoute = `<?php echo e(route('scripts.api.void', ['orderId' => '::ID'])); ?>`;


    datatableElement = $('#void_dt');
    searchElement = $('#void_search');

    columnArray = [{
            field: 'checkbox',
            title: '<label class="checkbox checkbox-single checkbox-all"><input type="checkbox" id="select-all-checkbox" />&nbsp;<span></span></label>',
            sortable: false,
            width: 'auto',
            autoHide: false,
            textAlign: 'center',
            template: function(data) {
                // Check if this ID is in our global selected IDs
                const isChecked = globalSelectedIds.includes(data.id.toString()) ? 'checked' : '';

                return `<label class="checkbox checkbox-single">
                            <input type="checkbox" class="row-checkbox" value="${data.id}" ${isChecked} />&nbsp;<span></span>
                        </label>`;
            }
        },
        {
            field: 'import_file_name',
            title: `File Name`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
            }
        },
        {
            field: 'created_at',
            title: `Created At`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
            }
        },
        {
            field: 'sent_at',
            title: `Sent at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Sent Yet</b>';
            }
        },
        {
            field: 'script_date',
            title: `Script date`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return moment(data.script_date).format('MM/DD/YYYY');
            }
        },
        {
            field: 'last_name',
            title: `Last name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'first_name',
            title: `First name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'medication',
            title: `Medication`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'provider_name',
            title: `Provider Name`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                if (data.import && data.import.user && data.import.user.id) {
                    return `<a href="${userEditRoute.replace('::ID', data.import.user.id)}" class="text-primary font-weight-bold" data-toggle="tooltip" title="Edit Provider">
                        ${data.provider_name}
                    </a>`;
                } else {
                    return data.provider_name || '';
                }
            }
        },
        {
            field: 'status',
            title: `Status`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'order_id',
            title: `Order ID`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.order_id ? data.order_id : '-';
            }
        },
        {
            field: 'Actions',
            title: 'Actions',
            width: 'auto',
            sortable: false,
            overflow: 'visible',
            autoHide: false,
            template: function(data) {
                const viewRoute = `<?php echo e(route('scripts.show-pdf', ['importFile' => '::ID'])); ?>`;
                const webhookRoute = `<?php echo e(route('scripts.webhook', ['id' => '::ID'])); ?>`;
                // const voidRoute = `<?php echo e(route('scripts.api.void', ['orderId' => '::ID'])); ?>`;
                const downloadRoute = `<?php echo e(route('archive.file-download', ['id' => '::ID'])); ?>`.replace('::ID',
                    data.id);
                const viewBtn = `
                                    <a href="#" data-id="${data.id}"
                                       data-view-route="${viewRoute.replace('::ID', data.id)}"
                                       data-download-route="${downloadRoute}"
                                       class="btn btn-sm btn-clean btn-icon preview-btn" data-toggle="tooltip" title="View Script">
                                        <i class="menu-icon fas fa-eye"></i>
                                    </a>`;
                const webhookdBtn = `
                                    <a href="${webhookRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Webhook">
                                        <i class="fas fa-link"></i>
                                    </a>`;
                const downloadBtn = `
                                    <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download Script">
                                        <i class="menu-icon fas fa-download"></i>
                                    </a>`;
                return downloadBtn + viewBtn + webhookdBtn;
            }
        }
    ];

    datatable = datatableElement.KTDatatable({
        data: {
            type: 'remote',
            source: {
                read: {
                    url: apiRoute,
                    //sample custom headers
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    params: function() {
                        // Get the current query parameters
                        const query = datatable.getDataSourceQuery();

                        // Get the current search value directly from the search input
                        const searchValue = $(searchElement).val() || '';

                        // Add filter parameters to the request
                        return {
                            displayed_ids: globalSelectedIds,
                            sent_date: query.sent_date || '',
                            provider_id: query.provider_id || '',
                            medication_id: query.medication_id || '',
                            clinic_name: query.clinic_name || '',
                            search: searchValue,
                            query: {
                                sent_date: query.sent_date || '',
                                provider_id: query.provider_id || '',
                                medication_id: query.medication_id || '',
                                clinic_name: query.clinic_name || '',
                                search: searchValue
                            }
                        };
                    },
                    map: function(raw) {
                        // sample data mapping
                        var dataSet = raw;
                        if (typeof raw.data !== 'undefined') {
                            dataSet = raw.data;
                        }

                        // After data is loaded, update the "Select All" checkbox state
                        setTimeout(function() {
                            updateSelectAllCheckboxState();
                        }, 100);

                        return dataSet;
                    }
                },
            },
            pageSize: 10,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        },
        pagination: true,
        search: {
            input: searchElement,
            key: 'search',
            delay: 500
        },
        layout: {
            customScrollbar: false,
            scroll: true,
        },
        columns: columnArray
    });

    // Initialize the datatable query parameters with empty values
    datatable.setDataSourceQuery({
        query: {
            sent_date: '',
            provider_id: '',
            medication_id: '',
            clinic_name: '',
            search: ''
        }
    });

    // Function to disable/enable all buttons on the page
    function togglePageButtons(disable) {
        // Disable/enable only the "All" buttons
        $('#download-all-global-btn').prop('disabled', disable);

        // If disabling all buttons due to empty data, also disable the "Selected" buttons
        if (disable) {
            toggleSelectedButtons(true);
        }
    }

    // Function to toggle the "Selected" buttons based on whether any items are selected
    function toggleSelectedButtons(disable) {
        $('#download-selected-global-btn').prop('disabled', disable);
    }

    // Initialize the "Selected" buttons as disabled by default
    toggleSelectedButtons(true);

    function toggleDownloadButtons(disable) {
        $('#download-all-global-btn').prop('disabled', disable);
    }

    toggleDownloadButtons(false);

    // Handle ajax done event
    datatable.on('datatable-on-ajax-done', function(e, data) {
        // Check if data is empty
        const isEmpty = !data || !data.length;
        togglePageButtons(isEmpty);

        // After data is loaded, restore checkboxes for selected IDs
        setTimeout(function() {
            // Restore checkboxes for items that were previously selected
            restoreSelectedCheckboxes();

            // Update "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Update the "Selected" buttons state
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle ajax fail event
    datatable.on('datatable-on-ajax-fail', function(e, jqXHR) {
        // Disable buttons on error
        togglePageButtons(true);
    });

    const routeTemplate = "<?php echo e(route('scripts.download-all-pdf')); ?>";

    $('#download-all-global-btn').on('click', function() {
        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_VOIDED); ?>' // <-- Add this to send status in request
        }));

        // Get current filter values and add them to the form
        const sentDate = $('#sent_date_filter').val();
        if (sentDate) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'sent_date',
                value: sentDate
            }));
        }

        const providerId = $('#provider_filter').val();
        if (providerId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'provider_id',
                value: providerId
            }));
        }

        const clinicName = $('#clinic_name_filter').val();
        if (clinicName) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'clinic_name',
                value: clinicName
            }));
        }

        const medicationId = $('#medication_filter').val();
        if (medicationId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'medication_id',
                value: medicationId
            }));
        }

        // Get current search value
        const searchValue = $(searchElement).val();
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#download-selected-global-btn').on('click', function() {
        const selectedIds = getSelectedIds();

        if (selectedIds.length === 0) {
            return;
        }

        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate.replace('__ID__', '') // same endpoint
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '<?php echo e(csrf_token()); ?>'
        }));

        // Optional: pass status filter if needed
        form.append($('<input>', {
            type: 'hidden',
            name: 'status[]',
            value: '<?php echo e(ImportFile::STATUS_VOIDED); ?>' // change dynamically if needed
        }));

        // Add displayed_ids[] inputs
        selectedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });

    // Function to update the "Select All" checkbox state
    function updateSelectAllCheckboxState() {
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;

        if (totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes) {
            $('#select-all-checkbox').prop('checked', true);
        } else {
            $('#select-all-checkbox').prop('checked', false);
        }
    }

    // Function to restore selected checkboxes based on globalSelectedIds
    function restoreSelectedCheckboxes() {
        // For each checkbox on the current page
        $('.row-checkbox').each(function() {
            const id = $(this).val();
            // If this ID is in our globalSelectedIds array, check it
            if (globalSelectedIds.includes(id)) {
                $(this).prop('checked', true);
            }
        });
    }

    // Handle pagination events to maintain selection state
    datatable.on('datatable-on-layout-updated', function() {
        // After layout update (which happens on page change), restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle datatable reloads
    datatable.on('datatable-on-reloaded', function() {
        // After reload, restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle page change events
    datatable.on('datatable-on-goto-page', function(e, meta) {
        // When changing pages, we need to preserve our globalSelectedIds
        // The datatable will reload data, and our ajax-done handler will restore selections
        console.log('Page changed to:', meta.page);
    });

    // Handle "Select All" checkbox
    $(document).on('change', '#select-all-checkbox', function() {
        let isChecked = $(this).is(':checked');

        // Check/uncheck all visible checkboxes
        $('.row-checkbox').prop('checked', isChecked);

        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update button states based on selection
        toggleSelectedButtons(globalSelectedIds.length === 0);
        toggleDownloadButtons(globalSelectedIds.length === 0 ? false : true);
    });

    // Handle individual checkbox changes
    $(document).on('change', '.row-checkbox', function() {
        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update "Select All" checkbox state
        updateSelectAllCheckboxState();
    });

    function getSelectedIds() {
        // Get all visible checkbox IDs on the current page
        let visibleIds = [];
        $('.row-checkbox').each(function() {
            visibleIds.push($(this).val());
        });

        // First, remove any IDs from globalSelectedIds that are visible on the current page
        // This ensures we don't have stale selections
        globalSelectedIds = globalSelectedIds.filter(function(id) {
            return !visibleIds.includes(id);
        });

        // Now add all currently checked IDs to globalSelectedIds
        $('.row-checkbox:checked').each(function() {
            const id = $(this).val();
            globalSelectedIds.push(id);
        });

        // Update button states
        toggleSelectedButtons(globalSelectedIds.length === 0);
        toggleDownloadButtons(globalSelectedIds.length === 0 ? false : true);

        return globalSelectedIds;
    }

    // Add event listener for preview buttons
    $(document).on('click', '.preview-btn', function(e) {
        e.preventDefault();

        const fileId = $(this).data('id');
        const viewRoute = $(this).data('view-route');
        const downloadRoute = $(this).data('download-route');

        // Set the download button URL
        $('#download-preview-btn').attr('href', downloadRoute);

        // Show the modal
        $('#scriptPreviewModal').modal('show');

        // Load the script preview
        $('#script-preview-content').html(
            '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
        );

        // Load the PDF in an iframe
        setTimeout(function() {
            $('#script-preview-content').html(
                `<iframe src="${viewRoute}" width="100%" height="100%" style="height: 90vh;" frameborder="0"></iframe>`
            );
        }, 500);
    });

    // Handle modal events
    $('#scriptPreviewModal').on('hidden.bs.modal', function() {
        // Clear the preview content when modal is closed
        $('#script-preview-content').html('');
    });

    // Add event handlers for filter elements
    $('#sent_date_filter, #provider_filter, #medication_filter, #clinic_name_filter').on('change', function() {
        // Get current filter values
        const sentDate = $('#sent_date_filter').val();
        const providerId = $('#provider_filter').val();
        const medicationId = $('#medication_filter').val();
        const clinicName = $('#clinic_name_filter').val();


        // Get current search value - this will preserve the search text when changing filters
        const searchValue = $(searchElement).val() || '';


        // Clear selected IDs when filters change
        globalSelectedIds = [];

        // Update button states
        toggleSelectedButtons(true);

        // Set the query parameters for the datatable
        datatable.setDataSourceQuery({
            sent_date: sentDate,
            provider_id: providerId,
            medication_id: medicationId,
            clinic_name: clinicName,
            search: searchValue,
            query: {
                sent_date: sentDate,
                provider_id: providerId,
                medication_id: medicationId,
                clinic_name: clinicName,
                search: searchValue
            }
        });

        // Reload the datatable with the new query parameters
        datatable.reload();
    });

    // Clear date filter
    $('#clear_date_filter').on('click', function() {
        $('#sent_date_filter').val('');

        // Update the query parameters
        const providerId = $('#provider_filter').val();
        const medicationId = $('#medication_filter').val();
        const clinicName = $('#clinic_name_filter').val();


        // Get current search value - this will preserve the search text when clearing date filter
        const searchValue = $(searchElement).val() || '';

        // Clear selected IDs when filters change
        globalSelectedIds = [];

        // Update button states
        toggleSelectedButtons(true);

        // Set the query parameters for the datatable
        datatable.setDataSourceQuery({
            sent_date: '',
            provider_id: providerId,
            medication_id: medicationId,
            clinic_name: clinicName,
            search: searchValue,
            query: {
                sent_date: '',
                provider_id: providerId,
                medication_id: medicationId,
                clinic_name: clinicName,
                search: searchValue
            }
        });

        // Reload the datatable
        datatable.reload();
    });

    // Simple row click handler
    $(document).on('click', '#void_dt tbody tr td', function(e) {
        // Skip if clicking on the checkbox cell or actions cell
        if ($(this).is(':first-child') || $(this).is(':last-child') ||
            $(e.target).is('input[type="checkbox"]') ||
            $(e.target).closest('a').length ||
            $(e.target).closest('button').length ||
            $(e.target).closest('i').length) {
            return;
        }

        // Find the checkbox in the first cell
        const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

        // Toggle the checkbox
        checkbox.prop('checked', !checkbox.prop('checked'));

        // Trigger change event
        checkbox.trigger('change');
    });


    // Store current page data for status lookup
    let currentPageData = [];
    datatable.on('datatable-on-ajax-done', function(e, data) {
        currentPageData = data || [];
        // Check if data is empty
        const isEmpty = !data || !data.length;
        togglePageButtons(isEmpty);

        // After data is loaded, restore checkboxes for selected IDs
        setTimeout(function() {
            // Restore checkboxes for items that were previously selected
            restoreSelectedCheckboxes();

            // Update "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Update the "Selected" buttons state
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/scripts/index-void.blade.php ENDPATH**/ ?>