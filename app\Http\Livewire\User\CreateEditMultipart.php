<?php

namespace App\Http\Livewire\User;

use App\Http\Livewire\Traits\HasMultipartForm;
use App\Http\Livewire\Support\MultipartFormBuilder;
use App\Mail\ForgotPasswordLinkMail;
use App\Mail\WelcomeUserMail;
use App\Models\State;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CreateEditMultipart extends Component
{
    use WithFileUploads, HasMultipartForm;

    public User $user;
    public $signature;
    public $states = [];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadStates();
        $this->initializeMultipartForm();

        // For new users, initialize signature as null
        if (!$user->id) {
            $this->signature = null;
        }
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    /**
     * Configure the multipart form steps
     */
    protected function getMultipartConfig()
    {
        return MultipartFormBuilder::create('Provider Registration')
            ->showProgressBar(true)
            ->allowStepNavigation(false)
            ->addStep('Basic Information', 'Enter basic personal and contact information')
                ->view('livewire.user.multipart.step-basic-info')
                ->fields(['user.first_name', 'user.last_name', 'user.email', 'user.printed_name', 'user.clinic_name'])
                ->rules([
                    'user.first_name' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
                    'user.last_name' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
                    'user.email' => $this->user->id
                        ? 'required|email|unique:users,email,' . $this->user->id
                        : 'required|email|unique:users,email',
                    'user.printed_name' => 'required|max:100',
                    'user.clinic_name' => 'nullable|max:50',
                ])
                ->messages([
                    'user.first_name.regex' => 'The first name field may only contain letters and spaces.',
                    'user.last_name.regex' => 'The last name field may only contain letters and spaces.',
                ])
            ->addStep('Professional Information', 'Enter professional credentials and license information')
                ->view('livewire.user.multipart.step-professional-info')
                ->fields(['user.NPI#', 'user.LIC#', 'user.DEA#'])
                ->rules([
                    'user.NPI#' => $this->user->id
                        ? 'required|digits:10|unique:users,NPI#,' . $this->user->id
                        : 'required|digits:10|unique:users,NPI#',
                    'user.LIC#' => [
                        'nullable',
                        function ($attribute, $value, $fail) {
                            if ($value && strlen(str_replace('-', '', $value)) < 7) {
                                $fail('The LIC# must have at least 7 characters excluding dashes.');
                            }
                        },
                    ],
                    'user.DEA#' => [
                        'nullable',
                        function ($attribute, $value, $fail) {
                            if ($value) {
                                $cleanValue = preg_replace('/[^a-zA-Z0-9]/', '', $value);
                                if (!preg_match('/^[a-zA-Z]{2}[0-9]{7}$/', $cleanValue)) {
                                    $fail('DEA must be 9 characters: two letters followed by seven digits.');
                                }
                            }
                        }
                    ],
                ])
                ->messages([
                    'user.NPI#.required' => 'The NPI# field is required.',
                    'user.NPI#.digits' => 'The NPI# must be 10 digits.',
                    'user.NPI#.unique' => 'The NPI# has already been taken.',
                    'user.DEA#.required' => 'The DEA# field is required.',
                ])
            ->addStep('Contact & Dispatch', 'Enter contact information and dispatch preferences')
                ->view('livewire.user.multipart.step-contact-dispatch')
                ->fields(['user.phone', 'user.fax', 'user.default_dispatch_method', 'user.dispense_abbreviation'])
                ->rules([
                    'user.phone' => ['nullable', 'max:15'],
                    'user.fax' => ['nullable', 'regex:/^[2-9][0-9]{9}$/'],
                    'user.default_dispatch_method' => 'required',
                    'user.dispense_abbreviation' => $this->user['default_dispatch_method'] === User::DISPATCH_METHOD_DISPENSE_PRO 
                        ? ['required', Rule::unique('users', 'dispense_abbreviation')->ignore($this->user['id'] ?? null)]
                        : 'nullable',
                ])
                ->messages([
                    'user.fax.regex' => 'Fax number must be exactly 10 digits and cannot start with 0 or 1.',
                    'user.dispense_abbreviation.unique' => 'The dispense abbreviation must be unique.',
                ])
            ->addStep('Address Information', 'Enter address and location details')
                ->view('livewire.user.multipart.step-address')
                ->fields(['user.address', 'user.city', 'user.state_id', 'user.zip'])
                ->rules([
                    'user.address' => 'nullable|max:256',
                    'user.city' => 'nullable|max:256',
                    'user.state_id' => 'nullable',
                    'user.zip' => 'nullable|regex:/^\d{5}(-\d{4})?$/',
                ])
                ->messages([
                    'user.zip.regex' => 'The ZIP code must be in the format 12345 or 12345-6789. i.e Format of 5+4.',
                ])
            ->addStep('Signature Upload', 'Upload your digital signature')
                ->view('livewire.user.multipart.step-signature')
                ->fields(['signature'])
                ->rules([
                    'signature' => $this->user->id
                        ? 'nullable|image|mimes:jpg,jpeg,png,gif,webp,bmp|max:2048'
                        : 'required|image|mimes:jpg,jpeg,png,gif,webp,bmp|max:2048',
                ])
            ->build();
    }

    /**
     * Get all validation rules (used by Livewire)
     */
    public function rules()
    {
        return $this->getAllValidationRules();
    }

    /**
     * Get all validation messages (used by Livewire)
     */
    public function messages()
    {
        return $this->getAllValidationMessages();
    }

    /**
     * Store the user (called when form is submitted)
     */
    public function store()
    {
        // Ensure email is trimmed before validation
        if ($this->user->email) {
            $this->user->email = trim($this->user->email);
        }

        try {
            // Check if this is an existing user and if the email has been changed
            $emailChanged = false;
            if ($this->user->id) {
                $originalUser = User::find($this->user->id);
                if ($originalUser && $originalUser->email !== $this->user->email) {
                    $emailChanged = true;
                }
            }

            // Format fields to capitalize letters when form is submitted
            $this->user->{'LIC#'} = $this->formatLicenseNumber($this->user->{'LIC#'});
            $this->user->{'DEA#'} = $this->formatDEANumber($this->user->{'DEA#'});

            if ($this->user->default_dispatch_method == User::DISPATCH_METHOD_FAX) {
                $this->user->dispense_abbreviation = null;
            }

            // Handle signature upload
            if ($this->signature) {
                // Delete old signature if it exists and is not a default image
                if (isset($this->user->id) && $this->user->signature && !str_contains($this->user->signature, 'default')) {
                    try {
                        Storage::delete($this->user->signature);
                    } catch (\Throwable $th) {
                        // Silently handle deletion errors
                    }
                }

                try {
                    $path = $this->signature->store('signatures', 'public');
                    if (!$path) {
                        throw new \Exception("Failed to store signature file");
                    }
                    $this->user->signature = $path;
                } catch (\Throwable $e) {
                    session()->flash('error-message', __('messages.error_uploading_signature', ['error' => $e->getMessage()]));
                    return redirect()->back();
                }
            }

            // Set role to 'provider' if not set
            $this->user->role = User::ROLE_PROVIDER;

            // Only for new users: set password and send welcome email
            if (!$this->user->id) {
                $randomPassword = Str::random(8);
                $this->user->password = bcrypt($randomPassword);
                $this->user->password_changed_at = null;

                $this->user->save();
                LogService::logProviderCreated($this->user);

                try {
                    Mail::to($this->user->email)->send(new WelcomeUserMail($this->user, $randomPassword));
                    LogService::logTempPasswordSent($this->user);
                    $message = __('messages.user_created_successfully', ['email' => $this->user->email]);
                } catch (\Throwable $e) {
                    $message = __('messages.user_created_successfully_but_email_failed', ['password' => $randomPassword]);
                }
            } else {
                // Handle user updates
                $originalValues = [];
                if ($this->user && method_exists($this->user, 'getOriginal')) {
                    $originalValues = LogService::getModelAttributes($this->user->getOriginal(), [
                        'first_name', 'last_name', 'email', 'phone', 'license_number', 'dea_number', 'npi_number', 'is_active'
                    ]);
                }

                $this->user->save();

                $newValues = LogService::getModelAttributes($this->user, [
                    'first_name', 'last_name', 'email', 'phone', 'license_number', 'dea_number', 'npi_number', 'is_active'
                ]);

                LogService::logProviderEdited($this->user, $originalValues, $newValues);

                if ($emailChanged) {
                    $randomPassword = Str::random(8);
                    $this->user->password = bcrypt($randomPassword);
                    $this->user->password_changed_at = null;
                    $this->user->save();

                    try {
                        Mail::to($this->user->email)->send(new ForgotPasswordLinkMail($randomPassword, $this->user));
                        LogService::logTempPasswordSent($this->user);
                        $message = __('messages.user_updated_successfully_with_temp_password');
                    } catch (\Exception $e) {
                        Log::error('Failed to send temporary password email after email change', [
                            'email' => $this->user->email, 'error' => $e->getMessage()
                        ]);
                        $message = __('messages.user_updated_successfully_but_email_failed', ['password' => $randomPassword]);
                    }
                } else {
                    $message = __('messages.user_updated_successfully');
                }
            }

            session()->flash('success-message', $message);
        } catch (\Throwable $th) {
            session()->flash('error-message', $th->getMessage());
        }

        return redirect()->route('users.index');
    }

    public function updated($propertyName)
    {
        // Trim email when it's updated
        if ($propertyName === 'user.email' && $this->user->email) {
            $this->user->email = trim($this->user->email);
        }

        $this->validateOnly($propertyName);
        $this->emit('contentChanged');
    }

    private function formatLicenseNumber($value)
    {
        if (empty($value)) return $value;
        return preg_replace_callback('/[a-zA-Z]+/', function ($matches) {
            return strtoupper($matches[0]);
        }, $value);
    }

    private function formatDEANumber($value)
    {
        if (empty($value)) return $value;
        $cleanValue = preg_replace('/[^a-zA-Z0-9]/', '', $value);
        $letters = strtoupper(substr($cleanValue, 0, 2));
        $digits = substr($cleanValue, 2);
        return $letters . $digits;
    }

    public function render()
    {
        return view('livewire.user.create-edit-multipart');
    }
}
