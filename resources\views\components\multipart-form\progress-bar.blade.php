@props([
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => [],
    'allowStepNavigation' => false
])

<div class="wizard-nav">
    <div class="wizard-steps">
        @for($i = 1; $i <= $totalSteps; $i++)
            @php
                $stepConfig = $steps[$i-1] ?? [];
                $isCompleted = method_exists($this, 'isStepCompleted') ? $this->isStepCompleted($i) : false;
                $hasErrors = method_exists($this, 'stepHasErrors') ? $this->stepHasErrors($i) : false;
                $title = $stepConfig['title'] ?? "Step {$i}";
                $description = $stepConfig['description'] ?? '';
            @endphp
            
            <div class="wizard-step {{ $i == $currentStep ? 'wizard-step-current' : '' }} {{ $isCompleted ? 'wizard-step-done' : '' }} {{ $hasErrors ? 'wizard-step-error' : '' }}" 
                 data-wizard-type="step" 
                 data-wizard-state="{{ $i == $currentStep ? 'current' : ($isCompleted ? 'done' : 'pending') }}"
                 @if($allowStepNavigation && method_exists($this, 'goToStep')) 
                     wire:click="goToStep({{ $i }})" 
                     style="cursor: pointer;"
                 @endif>
                <div class="wizard-wrapper">
                    <div class="wizard-number">
                        @if($isCompleted)
                            <i class="ki ki-check"></i>
                        @elseif($hasErrors)
                            <i class="ki ki-close"></i>
                        @else
                            {{ $i }}
                        @endif
                    </div>
                    <div class="wizard-label">
                        <div class="wizard-title">{{ $title }}</div>
                        @if($description)
                            <div class="wizard-desc">{{ $description }}</div>
                        @endif
                    </div>
                </div>
            </div>
        @endfor
    </div>
</div>
