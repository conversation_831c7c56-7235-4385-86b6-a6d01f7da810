@props([
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => [],
    'allowStepNavigation' => false
])

<div class="wizard-nav">
    <div class="wizard-steps">
        @for($i = 1; $i <= $totalSteps; $i++)
            @php
                $stepConfig = $steps[$i-1] ?? [];
                $isCompleted = $i < $currentStep;
                $isCurrent = $i == $currentStep;
                $title = $stepConfig['title'] ?? "Step {$i}";
                $description = $stepConfig['description'] ?? '';
            @endphp

            <div class="wizard-step"
                 style="flex: 0 0 calc({{ 100 / $totalSteps }}% - 0.25rem); width: calc({{ 100 / $totalSteps }}% - 0.25rem);"
                 data-wizard-type="step"
                 data-wizard-state="{{ $isCurrent ? 'current' : ($isCompleted ? 'done' : 'pending') }}"
                 @if($allowStepNavigation)
                     wire:click="goToStep({{ $i }})"
                     style="cursor: pointer;"
                 @endif>
                <div class="wizard-wrapper">
                    <div class="wizard-number">
                        @if($isCompleted)
                            <i class="ki ki-check icon-sm"></i>
                        @else
                            {{ $i }}
                        @endif
                    </div>
                    <div class="wizard-label">
                        <div class="wizard-title">{{ $title }}</div>
                        @if($description)
                            <div class="wizard-desc">{{ $description }}</div>
                        @endif
                    </div>
                </div>
            </div>
        @endfor
    </div>
</div>
