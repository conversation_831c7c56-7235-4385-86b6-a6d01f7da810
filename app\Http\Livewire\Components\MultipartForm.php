<?php

namespace App\Http\Livewire\Components;

use Livewire\Component;
use Illuminate\Support\Facades\Validator;

class MultipartForm extends Component
{
    public $currentStep = 1;
    public $totalSteps = 1;
    public $steps = [];
    public $stepData = [];
    public $stepValidationRules = [];
    public $stepValidationMessages = [];
    public $formTitle = 'Multi-Step Form';
    public $showProgressBar = true;
    public $allowStepNavigation = false;
    public $completedSteps = [];
    public $stepErrors = [];

    protected $listeners = [
        'nextStep' => 'goToNextStep',
        'prevStep' => 'goToPrevStep',
        'goToStep' => 'goToStep',
        'validateCurrentStep' => 'validateCurrentStep',
        'submitForm' => 'submitForm',
        'resetForm' => 'resetForm',
        'validateAllSteps' => 'validateAllSteps'
    ];

    public function mount($config = [])
    {
        $this->initializeForm($config);
    }

    /**
     * Initialize the form with configuration
     */
    public function initializeForm($config)
    {
        $this->formTitle = $config['title'] ?? 'Multi-Step Form';
        $this->showProgressBar = $config['showProgressBar'] ?? true;
        $this->allowStepNavigation = $config['allowStepNavigation'] ?? false;
        
        if (isset($config['steps']) && is_array($config['steps'])) {
            $this->steps = $config['steps'];
            $this->totalSteps = count($this->steps);
            
            // Initialize step data and validation rules
            foreach ($this->steps as $index => $step) {
                $stepNumber = $index + 1;
                $this->stepData[$stepNumber] = $step['data'] ?? [];
                $this->stepValidationRules[$stepNumber] = $step['rules'] ?? [];
                $this->stepValidationMessages[$stepNumber] = $step['messages'] ?? [];
            }
        }
        
        // Initialize completed steps array
        $this->completedSteps = array_fill(1, $this->totalSteps, false);
    }

    /**
     * Get current step configuration
     */
    public function getCurrentStepConfig()
    {
        return $this->steps[$this->currentStep - 1] ?? [];
    }

    /**
     * Get step title
     */
    public function getStepTitle($stepNumber = null)
    {
        $step = $stepNumber ? $stepNumber : $this->currentStep;
        return $this->steps[$step - 1]['title'] ?? "Step {$step}";
    }

    /**
     * Get step description
     */
    public function getStepDescription($stepNumber = null)
    {
        $step = $stepNumber ? $stepNumber : $this->currentStep;
        return $this->steps[$step - 1]['description'] ?? '';
    }

    /**
     * Validate current step
     */
    public function validateCurrentStep()
    {
        $rules = $this->stepValidationRules[$this->currentStep] ?? [];
        $messages = $this->stepValidationMessages[$this->currentStep] ?? [];
        
        if (empty($rules)) {
            return true;
        }

        // Get data for current step
        $dataToValidate = [];
        foreach ($rules as $field => $rule) {
            $dataToValidate[$field] = $this->getNestedProperty($field);
        }

        $validator = Validator::make($dataToValidate, $rules, $messages);
        
        if ($validator->fails()) {
            $this->stepErrors[$this->currentStep] = $validator->errors()->toArray();
            
            // Set Livewire errors
            foreach ($validator->errors()->toArray() as $field => $errors) {
                $this->addError($field, $errors[0]);
            }
            
            return false;
        }

        // Clear step errors if validation passes
        unset($this->stepErrors[$this->currentStep]);
        $this->completedSteps[$this->currentStep] = true;
        
        return true;
    }

    /**
     * Go to next step
     */
    public function goToNextStep()
    {
        if ($this->validateCurrentStep() && $this->currentStep < $this->totalSteps) {
            $this->currentStep++;
            $this->clearErrorBag();
            $this->emit('stepChanged', $this->currentStep);
        }
    }

    /**
     * Go to previous step
     */
    public function goToPrevStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
            $this->clearErrorBag();
            $this->emit('stepChanged', $this->currentStep);
        }
    }

    /**
     * Go to specific step
     */
    public function goToStep($stepNumber)
    {
        if ($this->allowStepNavigation && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            // Validate all steps before the target step
            $canNavigate = true;
            for ($i = 1; $i < $stepNumber; $i++) {
                if (!$this->completedSteps[$i]) {
                    $canNavigate = false;
                    break;
                }
            }
            
            if ($canNavigate) {
                $this->currentStep = $stepNumber;
                $this->clearErrorBag();
                $this->emit('stepChanged', $this->currentStep);
            }
        }
    }

    /**
     * Check if step is completed
     */
    public function isStepCompleted($stepNumber)
    {
        return $this->completedSteps[$stepNumber] ?? false;
    }

    /**
     * Check if step has errors
     */
    public function stepHasErrors($stepNumber)
    {
        return isset($this->stepErrors[$stepNumber]) && !empty($this->stepErrors[$stepNumber]);
    }

    /**
     * Check if current step is first
     */
    public function isFirstStep()
    {
        return $this->currentStep === 1;
    }

    /**
     * Check if current step is last
     */
    public function isLastStep()
    {
        return $this->currentStep === $this->totalSteps;
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage()
    {
        return round(($this->currentStep / $this->totalSteps) * 100);
    }

    /**
     * Submit the complete form
     */
    public function submitForm()
    {
        if ($this->validateAllSteps()) {
            $this->emit('formSubmitted', $this->stepData);
        } else {
            // Go to first step with errors
            $this->goToFirstStepWithErrors();
            $this->emit('formValidationFailed', $this->stepErrors);
        }
    }

    /**
     * Validate all steps
     */
    public function validateAllSteps()
    {
        $allValid = true;
        $originalStep = $this->currentStep;

        for ($i = 1; $i <= $this->totalSteps; $i++) {
            $this->currentStep = $i;
            if (!$this->validateCurrentStep()) {
                $allValid = false;
            }
        }

        $this->currentStep = $originalStep;
        return $allValid;
    }

    /**
     * Go to first step with errors
     */
    public function goToFirstStepWithErrors()
    {
        foreach ($this->stepErrors as $stepNumber => $errors) {
            if (!empty($errors)) {
                $this->currentStep = $stepNumber;
                $this->clearErrorBag();
                $this->emit('stepChanged', $this->currentStep);
                break;
            }
        }
    }

    /**
     * Reset the entire form
     */
    public function resetForm()
    {
        $this->currentStep = 1;
        $this->stepData = [];
        $this->stepErrors = [];
        $this->completedSteps = array_fill(1, $this->totalSteps, false);
        $this->clearErrorBag();
        $this->emit('formReset');
        $this->emit('stepChanged', $this->currentStep);
    }

    /**
     * Get steps summary for review
     */
    public function getStepsSummary()
    {
        $summary = [];
        for ($i = 1; $i <= $this->totalSteps; $i++) {
            $summary[$i] = [
                'title' => $this->getStepTitle($i),
                'completed' => $this->isStepCompleted($i),
                'hasErrors' => $this->stepHasErrors($i),
                'data' => $this->stepData[$i] ?? []
            ];
        }
        return $summary;
    }

    /**
     * Helper method to get nested property value
     */
    protected function getNestedProperty($property)
    {
        $keys = explode('.', $property);
        $value = $this;
        
        foreach ($keys as $key) {
            if (is_object($value) && property_exists($value, $key)) {
                $value = $value->$key;
            } elseif (is_array($value) && array_key_exists($key, $value)) {
                $value = $value[$key];
            } else {
                return null;
            }
        }
        
        return $value;
    }

    public function render()
    {
        return view('livewire.components.multipart-form');
    }
}
