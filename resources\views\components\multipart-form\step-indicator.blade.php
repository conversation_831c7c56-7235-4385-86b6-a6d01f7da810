@props([
    'stepNumber' => 1,
    'title' => '',
    'isCompleted' => false,
    'hasErrors' => false,
    'isCurrent' => false,
    'allowNavigation' => false
])

<div class="step-indicator {{ $isCurrent ? 'current' : '' }} {{ $isCompleted ? 'completed' : '' }} {{ $hasErrors ? 'error' : '' }}"
     @if($allowNavigation) wire:click="goToStep({{ $stepNumber }})" style="cursor: pointer;" @endif>
    <div class="step-number">
        @if($isCompleted)
            <i class="ki ki-check"></i>
        @elseif($hasErrors)
            <i class="ki ki-close"></i>
        @else
            {{ $stepNumber }}
        @endif
    </div>
    @if($title)
        <div class="step-title">{{ $title }}</div>
    @endif
</div>

@push('styles')
<style>
    .step-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        transition: all 0.3s ease;
    }
    
    .step-indicator.current .step-number {
        background-color: #3699ff;
        color: white;
        border-color: #3699ff;
    }
    
    .step-indicator.completed .step-number {
        background-color: #1bc5bd;
        color: white;
        border-color: #1bc5bd;
    }
    
    .step-indicator.error .step-number {
        background-color: #f64e60;
        color: white;
        border-color: #f64e60;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid #e4e6ef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        background-color: #f3f6f9;
        color: #7e8299;
        margin-bottom: 0.5rem;
    }
    
    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #7e8299;
        text-align: center;
    }
    
    .step-indicator.current .step-title {
        color: #3699ff;
    }
    
    .step-indicator.completed .step-title {
        color: #1bc5bd;
    }
    
    .step-indicator.error .step-title {
        color: #f64e60;
    }
</style>
@endpush
