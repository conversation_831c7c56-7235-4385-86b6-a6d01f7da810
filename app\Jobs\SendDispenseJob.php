<?php

namespace App\Jobs;

use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\State;
use App\Models\User;
use App\Traits\FaxManager;
use Illuminate\Support\Str;
use App\Services\LogService;
use App\Services\ProgressTrackingService;
use App\Traits\DispenseProManager;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SendDispenseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $ids;
    protected User $userId;
    public $metadata = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $ids, $user_id,  $metadata = [])
    {
        $this->ids = $ids;
        $this->userId = $user_id;
        $this->metadata = $metadata;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $import_files = ImportFile::whereIn('id', $this->ids)->with('import')->get();

        $total = count($this->ids);
        $processed = 0;

        $progressTrackingService = new ProgressTrackingService();
        $progressTrackingService->manageProgress("queue_progress", [
            'processed' => $processed,
            'total' => $total,
        ], 30);

        foreach ($import_files as $import_file) {
            Log::info('SEND FILE TO DISPENSE PRO WITH ID ' . $import_file->id . '');
            try {
                // $import_file = ImportFile::where('id', $id)->with('import')->firstOrFail();
                $customer_dtls =  $import_file->import->user;
                $practice = null;
                
                if ($import_file->import->file_name == 'API Entry') {
                    $practice = $import_file->import->user->practice;
                }


                $state = State::where('short_name', $import_file->state)->first();
                $stateName = $state ? $state->name : null;

                $medication = Medication::where('name', $import_file->medication)->first();
                $ndc = $medication->ndc ?? null;

                $filePath = $import_file->file_path;

                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($filePath)) {
                    $pdf = Storage::disk('public')->get($filePath);
                } elseif (Storage::exists($filePath)) {
                    $pdf = Storage::get($filePath);
                }

                if($ndc === null) {
                    LogService::NdcNotFound($import_file->medication, $import_file->id,$this->userId);
                    
                    $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                    $import_file->save();
                    return;
                }

                if($stateName === null) {
                    LogService::StateNotFound($import_file->state, $import_file->id, $this->userId);
                    $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                    $import_file->save();
                    return;
                }

                if (!$pdf) {
                    LogService::logPdfNotFound($import_file->id, $this->userId);
                    $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                    $import_file->save();
                    return;
                }

                $pdf = base64_encode($pdf);
                $orderId = $import_file->order_id ? $import_file->order_id : strtoupper(Str::random(3)) . rand(10000, 99999);

                $data = [
                    "action" => "order",
                    "orderId" => $orderId,
                    "shipTo" => $import_file->ship_to,
                    "privacyConsent" => "false",
                    "patient" => [
                        // "lastName" => "MAXLIFE",
                        // "firstName" => "TEST",
                        // TODO::uncomment below lines for production
                        "lastName" => $import_file->last_name,
                        "firstName" => $import_file->first_name,
                        "address" => $import_file->address,
                        "address2" => "",
                        "city" => $import_file->city,
                        "region" => $stateName,
                        "postalCode" => $import_file->zip,
                        "countryCode" => '',
                        "dob" => Carbon::parse($import_file->dob)->format('Ymd'),
                        "gender" => $import_file->gender,
                        "phone" => $import_file->phone,
                    ],
                    "customer" => [
                        "abbreviation" => $practice?->dispensepro_abbreviation ?? $customer_dtls->dispense_abbreviation,
                        "name" => $customer_dtls->printed_name,
                        "address" => $customer_dtls->address,
                        "address2" => "",
                        "city" => $customer_dtls->city,
                        "region" => $customer_dtls->state->name ?? null,
                        "postalCode" => $customer_dtls->zip,
                        "phone" => $customer_dtls->phone
                    ],
                    "hasPrescription" => "true",
                    "rxs" => [[
                        "note" => $import_file->notes,
                        "description" => $medication->dispensepro_medication_name,
                        "quantity" => $import_file->vial_quantity,
                        "units" => "each",
                        "ndc" => $ndc,
                        "daysSupply" => $import_file->days_supply,
                        "sig" => $import_file->sig,
                        "writtenDate" => Carbon::parse($import_file->script_date)->format('Ymd'),
                        "prescriberNPI" => $customer_dtls->{"NPI#"},
                        "refills" => $import_file->refills,
                    ]],
                    "pdfScript" => $pdf
                ];

                $response = DispenseProManager::DispenseProCreateOrder($data);
                $response['orderId'] = $orderId;

                if ($response['status'] == 'ok') {

                    $import_file->order_id = $orderId;
                    $import_file->status = ImportFile::STATUS_SENT;
                    $import_file->sent_at = Carbon::now();
                    $import_file->save();

                    LogService::logDispenseProOrderCreated($response, $this->userId);
                    Log::info("DispensePro order sent successfully for ImportFile ID: {$import_file->id}");
                } else {

                    $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                    $import_file->save();

                    Log::warning("DispensePro order failed for ImportFile ID: {$import_file->id}, reason: " . json_encode($response['errorMessages']));
                    LogService::logDispenseProOrderFailed(
                        is_array($response['errorMessages']) ? implode(', ', $response['errorMessages']) : $response['errorMessages'],
                        $response,
                        $this->userId
                    );
                }
            } catch (Exception $e) {

                if (isset($import_file)) {
                    $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                    $import_file->save();

                    Log::info("Set status to PENDING_APPROVAL for ImportFile ID: {$import_file->id}");
                }

                Log::error("Error processing ImportFile ID {$import_file->id}: " . $e->getMessage());
            }

            $processed++;

            $progressTrackingService->updateProgress("queue_progress", [
                'processed' => $processed,
                'total' => $total,
            ]);
        }
        // foreach ($this->ids as $id) {
        // try {
        //     $import_file = ImportFile::where('id', $id)->with('import')->firstOrFail();
        //     $customer_dtls = $import_file->import->user;
        //     $stateName = State::where('short_name', $import_file->state)->value('name');
        //     $ndc = Medication::where('name', $import_file->medication)->value('ndc');

        //     $filePath = $import_file->value('file_path');
        //     $orderId = strtoupper(Str::random(3)) . rand(10000, 99999); // e.g., "XJF45783"

        //     $pdf = Storage::get($filePath);

        //     if ($pdf) {
        //         $pdf = base64_encode($pdf);
        //     } else {
        //         throw new Exception("PDF file not found or empty.");
        //     }

        //     $data = [
        //         "action" => "order",
        //         "orderId" => "$orderId", // random number generated
        //         // "shipTo" => "patient",
        //         // "billTo" => "patient",
        //         "privacyConsent" => "false", // Send SMS & Email tracking details
        //         "patient" => [
        //             // "id" => "**********", // customer patient id
        //             // "lastName" => $import_file->last_name,
        //             // "firstName" => $import_file->first_name,
        //             "lastName" => "MAXLIFE",
        //             "firstName" => "TEST",
        //             "address" => $import_file->address,
        //             "address2" => "",
        //             "city" => $import_file->city,
        //             "region" => $stateName,
        //             "postalCode" => $import_file->zip,
        //             "countryCode" => '',
        //             "dob" => Carbon::parse($import_file->dob)->format('Ymd'),
        //             "gender" => $import_file->gender,
        //             "phone" => $import_file->phone,
        //         ],
        //         "customer" => [
        //             "abbreviation" => $customer_dtls->dispense_abbreviation, // Assign customer to order
        //             "name" => $customer_dtls->printed_name,
        //             "address" => $customer_dtls->address,
        //             "address2" => "",
        //             "city" => $customer_dtls->city,
        //             "region" => $customer_dtls->state->name,
        //             "postalCode" => $customer_dtls->zip,
        //             "phone" => $customer_dtls->phone
        //         ],
        //         "hasPrescription" => "true", // Required
        //         "rxs" => [
        //             [
        //                 "description" => $import_file->notes,
        //                 "dose" => $import_file->dosing,
        //                 "quantity" => $import_file->vial_quantity,
        //                 "units" => "each",
        //                 "ndc" => $ndc, // NDC of medication or Compound CSN
        //                 "daysSupply" => 28,
        //                 "sig" => $import_file->sig,
        //                 "writtenDate" => $import_file->script_date,
        //                 "prescriberNPI" => **********, // required for each Rx
        //                 "refills" => $import_file->refills,
        //             ]
        //         ],
        //         "pdfScript" => $pdf // Base64 encoded prescription PDF
        //     ];

        //     $response = DispenseProManager::DispenseProCreateOrder($data);

        //     if ($response) {
        //         $import_file->status = ImportFile::STATUS_SENT;
        //         $import_file->sent_at = Carbon::now();
        //         $import_file->save();
        //             Log::info("DispensePro order sent successfully for ImportFile ID: {$id}");
        //     } else {
        //             Log::warning("DispensePro order failed for ImportFile ID: {$id}");
        //     }

        // } catch (Exception $e) {
        //         Log::error("Error processing ImportFile ID {$id}: " . $e->getMessage());
        //     }
        // }
    }
}
