<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => [],
    'allowStepNavigation' => false
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => [],
    'allowStepNavigation' => false
]); ?>
<?php foreach (array_filter(([
    'currentStep' => 1,
    'totalSteps' => 1,
    'steps' => [],
    'allowStepNavigation' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="wizard-nav">
    <div class="wizard-steps">
        <?php for($i = 1; $i <= $totalSteps; $i++): ?>
            <?php
                $stepConfig = $steps[$i-1] ?? [];
                $isCompleted = $i < $currentStep;
                $isCurrent = $i == $currentStep;
                $title = $stepConfig['title'] ?? "Step {$i}";
                $description = $stepConfig['description'] ?? '';
            ?>

            <div class="wizard-step"
                 style="flex: 0 0 calc(<?php echo e(100 / $totalSteps); ?>% - 0.25rem); width: calc(<?php echo e(100 / $totalSteps); ?>% - 0.25rem);"
                 data-wizard-type="step"
                 data-wizard-state="<?php echo e($isCurrent ? 'current' : ($isCompleted ? 'done' : 'pending')); ?>"
                 <?php if($allowStepNavigation): ?>
                     wire:click="goToStep(<?php echo e($i); ?>)"
                     style="cursor: pointer;"
                 <?php endif; ?>>
                <div class="wizard-wrapper">
                    <div class="wizard-number">
                        <?php if($isCompleted): ?>
                            <i class="ki ki-check icon-sm"></i>
                        <?php else: ?>
                            <?php echo e($i); ?>

                        <?php endif; ?>
                    </div>
                    <div class="wizard-label">
                        <div class="wizard-title"><?php echo e($title); ?></div>
                        <?php if($description): ?>
                            <div class="wizard-desc"><?php echo e($description); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endfor; ?>
    </div>
</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/multipart-form/progress-bar.blade.php ENDPATH**/ ?>