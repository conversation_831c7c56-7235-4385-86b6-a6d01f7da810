<?php

namespace App\Http\Livewire\Traits;

trait HasMultipartForm
{
    public $currentStep = 1;
    public $totalSteps = 1;
    public $multipartConfig = [];
    public $completedSteps = [];
    public $stepErrors = [];

    /**
     * Initialize multipart form
     */
    public function initializeMultipartForm()
    {
        $config = $this->getMultipartConfig();
        $this->multipartConfig = $config;
        $this->totalSteps = count($config['steps'] ?? []);
        $this->completedSteps = array_fill(1, $this->totalSteps, false);
    }

    /**
     * Get multipart form configuration
     * This method should be implemented in the component using this trait
     */
    abstract protected function getMultipartConfig();

    /**
     * Get current step configuration
     */
    public function getCurrentStepConfig()
    {
        return $this->multipartConfig['steps'][$this->currentStep - 1] ?? [];
    }

    /**
     * Get step title
     */
    public function getStepTitle($stepNumber = null)
    {
        $step = $stepNumber ? $stepNumber : $this->currentStep;
        return $this->multipartConfig['steps'][$step - 1]['title'] ?? "Step {$step}";
    }

    /**
     * Get step description
     */
    public function getStepDescription($stepNumber = null)
    {
        $step = $stepNumber ? $stepNumber : $this->currentStep;
        return $this->multipartConfig['steps'][$step - 1]['description'] ?? '';
    }

    /**
     * Validate current step
     */
    public function validateCurrentStep()
    {
        $stepConfig = $this->getCurrentStepConfig();
        $rules = $stepConfig['rules'] ?? [];
        $messages = $stepConfig['messages'] ?? [];
        
        if (empty($rules)) {
            $this->completedSteps[$this->currentStep] = true;
            return true;
        }

        // Validate using Livewire's built-in validation
        try {
            $this->validate($rules, $messages);
            
            // Clear step errors if validation passes
            unset($this->stepErrors[$this->currentStep]);
            $this->completedSteps[$this->currentStep] = true;
            
            return true;
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->stepErrors[$this->currentStep] = $e->errors();
            return false;
        }
    }

    /**
     * Go to next step
     */
    public function goToNextStep()
    {
        if ($this->validateCurrentStep() && $this->currentStep < $this->totalSteps) {
            $this->currentStep++;
            $this->resetErrorBag();
            $this->emit('stepChanged', $this->currentStep);
        }
    }

    /**
     * Go to previous step
     */
    public function goToPrevStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
            $this->resetErrorBag();
            $this->emit('stepChanged', $this->currentStep);
        }
    }

    /**
     * Go to specific step
     */
    public function goToStep($stepNumber)
    {
        $allowNavigation = $this->multipartConfig['allowStepNavigation'] ?? false;
        
        if ($allowNavigation && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            // Validate all steps before the target step
            $canNavigate = true;
            for ($i = 1; $i < $stepNumber; $i++) {
                if (!$this->completedSteps[$i]) {
                    $canNavigate = false;
                    break;
                }
            }
            
            if ($canNavigate) {
                $this->currentStep = $stepNumber;
                $this->resetErrorBag();
                $this->emit('stepChanged', $this->currentStep);
            }
        }
    }

    /**
     * Check if step is completed
     */
    public function isStepCompleted($stepNumber)
    {
        return $this->completedSteps[$stepNumber] ?? false;
    }

    /**
     * Check if step has errors
     */
    public function stepHasErrors($stepNumber)
    {
        return isset($this->stepErrors[$stepNumber]) && !empty($this->stepErrors[$stepNumber]);
    }

    /**
     * Check if current step is first
     */
    public function isFirstStep()
    {
        return $this->currentStep === 1;
    }

    /**
     * Check if current step is last
     */
    public function isLastStep()
    {
        return $this->currentStep === $this->totalSteps;
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage()
    {
        return round(($this->currentStep / $this->totalSteps) * 100);
    }

    /**
     * Submit the complete multipart form
     */
    public function submitMultipartForm()
    {
        // Validate all steps
        $allValid = true;
        $originalStep = $this->currentStep;
        
        for ($i = 1; $i <= $this->totalSteps; $i++) {
            $this->currentStep = $i;
            if (!$this->validateCurrentStep()) {
                $allValid = false;
            }
        }
        
        $this->currentStep = $originalStep;

        if ($allValid) {
            // Call the main store method
            return $this->store();
        } else {
            // Go to first step with errors
            foreach ($this->stepErrors as $stepNumber => $errors) {
                $this->currentStep = $stepNumber;
                break;
            }
            
            session()->flash('error-message', 'Please fix the errors in the form before submitting.');
            return false;
        }
    }

    /**
     * Get all form data for submission
     * This method can be overridden in the component to customize data collection
     */
    protected function getFormData()
    {
        $data = [];
        
        // Collect data from all steps
        foreach ($this->multipartConfig['steps'] as $index => $step) {
            if (isset($step['fields'])) {
                foreach ($step['fields'] as $field) {
                    $data[$field] = $this->getProperty($field);
                }
            }
        }
        
        return $data;
    }

    /**
     * Get property value using dot notation
     */
    protected function getProperty($property)
    {
        $keys = explode('.', $property);
        $value = $this;
        
        foreach ($keys as $key) {
            if (is_object($value) && property_exists($value, $key)) {
                $value = $value->$key;
            } elseif (is_array($value) && array_key_exists($key, $value)) {
                $value = $value[$key];
            } else {
                return null;
            }
        }
        
        return $value;
    }

    /**
     * Get validation rules for all steps
     */
    public function getAllValidationRules()
    {
        $rules = [];
        
        foreach ($this->multipartConfig['steps'] as $step) {
            if (isset($step['rules'])) {
                $rules = array_merge($rules, $step['rules']);
            }
        }
        
        return $rules;
    }

    /**
     * Get validation messages for all steps
     */
    public function getAllValidationMessages()
    {
        $messages = [];
        
        foreach ($this->multipartConfig['steps'] as $step) {
            if (isset($step['messages'])) {
                $messages = array_merge($messages, $step['messages']);
            }
        }
        
        return $messages;
    }
}
