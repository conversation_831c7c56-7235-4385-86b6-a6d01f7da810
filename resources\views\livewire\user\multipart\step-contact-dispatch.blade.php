@php
    use App\Models\User;
@endphp

<div class="row">
    <div class="col-md-6">
        <x-form.input 
            wire:model.defer="user.phone" 
            name="user.phone" 
            label="Phone Number" 
            placeholder="Enter phone number" />
    </div>
    <div class="col-md-6">
        <x-form.input 
            wire:model.defer="user.fax" 
            name="user.fax" 
            label="Fax Number" 
            placeholder="Enter 10-digit fax number" />
        <small class="form-text text-muted">Must be exactly 10 digits, cannot start with 0 or 1</small>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <x-form.select 
            wire:model.defer="user.default_dispatch_method" 
            name="user.default_dispatch_method" 
            label="Default Dispatch Method" 
            required="true">
            <option value="">Select Method</option>
            <option value="{{ User::DISPATCH_METHOD_FAX }}">Fax</option>
            <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}">DispensePro</option>
        </x-form.select>
    </div>
    <div class="col-md-6">
        @if($user['default_dispatch_method'] === User::DISPATCH_METHOD_DISPENSE_PRO)
            <x-form.input 
                wire:model.defer="user.dispense_abbreviation" 
                name="user.dispense_abbreviation" 
                label="Dispense Abbreviation" 
                required="true" 
                placeholder="Enter unique abbreviation" />
            <small class="form-text text-muted">Required for DispensePro method and must be unique</small>
        @else
            <div class="form-group">
                <label class="form-label text-muted">Dispense Abbreviation</label>
                <input type="text" class="form-control" disabled placeholder="Only required for DispensePro method">
                <small class="form-text text-muted">This field is only required when DispensePro is selected</small>
            </div>
        @endif
    </div>
</div>

<div class="alert alert-info mt-4">
    <div class="alert-icon">
        <i class="ki ki-information-circle"></i>
    </div>
    <div class="alert-text">
        <strong>Step 3 of 5:</strong> Configure your contact information and preferred dispatch method. If you select DispensePro, you must provide a unique abbreviation.
    </div>
</div>
