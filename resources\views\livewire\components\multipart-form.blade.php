<div class="wizard wizard-1" id="multipart-form-wizard" data-wizard-state="first" data-wizard-clickable="{{ $allowStepNavigation ? 'true' : 'false' }}">
    
    @if($showProgressBar)
        <!-- Progress Bar -->
        <div class="wizard-nav">
            <div class="wizard-steps">
                @for($i = 1; $i <= $totalSteps; $i++)
                    <div class="wizard-step {{ $i == $currentStep ? 'wizard-step-current' : '' }} {{ $this->isStepCompleted($i) ? 'wizard-step-done' : '' }} {{ $this->stepHasErrors($i) ? 'wizard-step-error' : '' }}" 
                         data-wizard-type="step" 
                         data-wizard-state="{{ $i == $currentStep ? 'current' : ($this->isStepCompleted($i) ? 'done' : 'pending') }}"
                         @if($allowStepNavigation) wire:click="goToStep({{ $i }})" @endif>
                        <div class="wizard-wrapper">
                            <div class="wizard-number">
                                @if($this->isStepCompleted($i))
                                    <i class="ki ki-check"></i>
                                @elseif($this->stepHasErrors($i))
                                    <i class="ki ki-close"></i>
                                @else
                                    {{ $i }}
                                @endif
                            </div>
                            <div class="wizard-label">
                                <div class="wizard-title">{{ $this->getStepTitle($i) }}</div>
                                @if($this->getStepDescription($i))
                                    <div class="wizard-desc">{{ $this->getStepDescription($i) }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endfor
            </div>
        </div>
    @endif

    <!-- Form Content -->
    <div class="wizard-body">
        <form wire:submit.prevent="submitForm">
            
            <!-- Step Content -->
            @for($i = 1; $i <= $totalSteps; $i++)
                <div class="wizard-content" 
                     data-wizard-type="step-content" 
                     data-wizard-state="{{ $i == $currentStep ? 'current' : '' }}"
                     style="{{ $i == $currentStep ? 'display: block;' : 'display: none;' }}">
                    
                    <x-card>
                        <x-card.body>
                            @if(isset($steps[$i-1]['view']))
                                @include($steps[$i-1]['view'])
                            @else
                                <!-- Default step content -->
                                <div class="text-center py-10">
                                    <h3>{{ $this->getStepTitle($i) }}</h3>
                                    @if($this->getStepDescription($i))
                                        <p class="text-muted">{{ $this->getStepDescription($i) }}</p>
                                    @endif
                                    <p class="text-muted">Step content should be defined in the configuration.</p>
                                </div>
                            @endif
                        </x-card.body>
                    </x-card>
                    
                </div>
            @endfor

            <!-- Navigation Buttons -->
            <div class="wizard-actions">
                <div class="d-flex justify-content-between">
                    <div>
                        @if(!$this->isFirstStep())
                            <button type="button" 
                                    class="btn btn-light-primary font-weight-bold btn-md px-9 py-4" 
                                    data-wizard-type="action-prev"
                                    wire:click="goToPrevStep">
                                <i class="ki ki-arrow-left icon-sm"></i>
                                Previous
                            </button>
                        @endif
                    </div>
                    
                    <div>
                        @if(!$this->isLastStep())
                            <button type="button" 
                                    class="btn btn-primary font-weight-bold btn-md px-9 py-4" 
                                    data-wizard-type="action-next"
                                    wire:click="goToNextStep">
                                Next
                                <i class="ki ki-arrow-right icon-sm"></i>
                            </button>
                        @else
                            <button type="submit" 
                                    class="btn btn-success font-weight-bold btn-md px-9 py-4" 
                                    data-wizard-type="action-submit"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="disabled">
                                <span wire:loading.remove>
                                    <i class="ki ki-check icon-sm"></i>
                                    Submit
                                </span>
                                <span wire:loading>
                                    <i class="fas fa-spinner fa-spin mr-1"></i>
                                    Submitting...
                                </span>
                            </button>
                        @endif
                    </div>
                </div>
            </div>

        </form>
    </div>

    <!-- Progress Info (Optional) -->
    @if($showProgressBar)
        <div class="wizard-info mt-4">
            <div class="progress">
                <div class="progress-bar bg-primary" 
                     role="progressbar" 
                     style="width: {{ $this->getProgressPercentage() }}%"
                     aria-valuenow="{{ $this->getProgressPercentage() }}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                    {{ $this->getProgressPercentage() }}%
                </div>
            </div>
            <div class="text-center mt-2">
                <small class="text-muted">Step {{ $currentStep }} of {{ $totalSteps }}</small>
            </div>
        </div>
    @endif

</div>

@push('styles')
<style>
    .wizard-step {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .wizard-step:not([data-wizard-clickable="true"]) {
        cursor: default;
    }
    
    .wizard-step-error .wizard-number {
        background-color: #f64e60 !important;
        color: white !important;
    }
    
    .wizard-step-error .wizard-title {
        color: #f64e60 !important;
    }
    
    .wizard-content {
        min-height: 300px;
    }
    
    .wizard-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #ebedf3;
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/multipart-form.js') }}"></script>
<script>
document.addEventListener('livewire:load', function () {
    // Initialize multipart form with custom options
    const multipartFormOptions = {
        enableKeyboardNavigation: true,
        autoScrollToStep: true,
        validateOnStepChange: true,
        showConfirmOnExit: false
    };

    // Initialize the form
    if (document.getElementById('multipart-form-wizard')) {
        const form = new MultipartForm('multipart-form-wizard', multipartFormOptions);

        // Custom event listeners
        document.getElementById('multipart-form-wizard').addEventListener('multipartForm:stepChanged', function(e) {
            console.log('Step changed to:', e.detail.step);
        });

        document.getElementById('multipart-form-wizard').addEventListener('multipartForm:formSubmitted', function(e) {
            console.log('Form submitted with data:', e.detail.data);
        });

        document.getElementById('multipart-form-wizard').addEventListener('multipartForm:formValidationFailed', function(e) {
            console.log('Form validation failed:', e.detail.errors);
        });
    }

    // Additional Livewire event listeners for backward compatibility
    window.livewire.on('stepChanged', function (step) {
        console.log('Livewire step changed:', step);
    });

    window.livewire.on('formSubmitted', function (data) {
        console.log('Livewire form submitted:', data);
    });

    window.livewire.on('formValidationFailed', function (errors) {
        console.log('Livewire form validation failed:', errors);
    });
});
</script>
@endpush
